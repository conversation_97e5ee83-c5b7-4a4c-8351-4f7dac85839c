#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psycopg2
import random
import string
import time
import os
import subprocess
from datetime import datetime

# 英文技术相关的单词，用于生成tag
TECH_WORDS = [
"art", "design", "technology", "innovation", "creativity", "music", "photography", "travel", "adventure", "nature", "food", "cooking", "fitness", "health", "wellness", "fashion", "style", "beauty", "makeup", "skincare", "lifestyle", "home", "decor", "interior", "architecture", "business", "entrepreneurship", "marketing", "finance", "investment", "education", "learning", "science", "research", "history", "culture", "literature", "writing", "books", "reading", "poetry", "storytelling", "film", "movies", "cinema", "television", "gaming", "esports", "virtualreality", "augmentedreality", "artificialintelligence", "machinelearning", "programming", "coding", "software", "development", "cybersecurity", "blockchain", "cryptocurrency", "sustainability", "environment", "climatechange", "renewableenergy", "greenliving", "ecofriendly", "recycling", "conservation", "wildlife", "animals", "pets", "gardening", "plants", "agriculture", "urbanfarming", "diy", "crafts", "handmade", "sewing", "knitting", "woodworking", "painting", "drawing", "sculpture", "ceramics", "jewelry", "accessories", "vintage", "retro", "antiques", "collectibles", "sports", "athletics", "yoga", "meditation", "mindfulness", "spirituality", "philosophy", "psychology", "selfcare", "mentalhealth", "motivation", "inspiration", "leadership", "teamwork", "productivity", "timemanagement", "organization", "minimalism", "decluttering", "travelphotography", "wanderlust", "backpacking", "camping", "hiking", "surfing", "skiing", "snowboarding", "cycling", "running", "swimming", "dance", "ballet", "hiphop", "jazz", "classicalmusic", "rock", "pop", "electronicmusic", "festival", "concert", "theater", "performance", "comedy", "standup", "magic", "illusion", "streetart", "graffiti", "mural", "museum", "gallery", "exhibition", "fashiondesign", "hautecouture", "streetwear", "sneakers", "watches", "luxury", "budget", "thrifting", "upcycling", "foodie", "gourmet", "vegan", "vegetarian", "glutenfree", "baking", "dessert", "coffee", "tea", "wine", "craftbeer", "mixology", "bartender", "restaurant", "cafe", "foodtruck", "markets", "shopping", "ecommerce", "socialmedia", "influencer", "contentcreation", "vlogging", "podcasting", "streaming", "journalism", "news", "politics", "activism", "charity", "volunteering", "community", "networking", "friendship", "family", "parenting", "kids", "educationtech", "onlinelearning", "tutoring", "language", "translation", "multicultural", "diversity", "inclusion"
]

# 数据库连接信息
DB_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "user": "postgres",
    "password": "postgres",
    "dbname": "brandreviews"
}

# GORM通常将struct名称转换为snake_case复数形式
TABLE_NAME = "tags"  # Tag -> tags

def generate_slug(name):
    """根据名称生成slug"""
    return name.lower().replace(" ", "-")

def check_table_exists(cursor, table_name=TABLE_NAME):
    """检查表是否存在"""
    cursor.execute("""
    SELECT EXISTS (
       SELECT FROM information_schema.tables 
       WHERE table_schema = 'public'
       AND table_name = %s
    );
    """, (table_name,))
    return cursor.fetchone()[0]

def run_migration():
    """运行Go迁移工具创建表"""
    print("尝试运行数据库迁移...")
    try:
        # 找到项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        
        # 构建migrate命令路径
        migrate_cmd = ["go", "run", "cmd/migrate/main.go"]
        
        # 切换到项目根目录并运行
        original_dir = os.getcwd()
        os.chdir(project_root)
        
        result = subprocess.run(migrate_cmd, 
                                stdout=subprocess.PIPE, 
                                stderr=subprocess.PIPE, 
                                text=True)
        
        # 切回原目录
        os.chdir(original_dir)
        
        if result.returncode == 0:
            print("迁移成功完成!")
            return True
        else:
            print(f"迁移失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"运行迁移时出错: {e}")
        return False

def generate_tags(cursor, count=100, featured_count=30, table_name=TABLE_NAME):
    """生成指定数量的标签数据"""
    # 确保featured_count不超过总数
    featured_count = min(featured_count, count)
    
    # 随机选择featured_count个索引作为featured标签
    featured_indices = random.sample(range(count), featured_count)
    
    # 已使用的tag名称集合
    used_tags = set()
    
    # 生成的tag数量
    generated_count = 0
    
    # 如果原始单词库不够，添加随机后缀
    all_words = TECH_WORDS.copy()
    
    # 当前时间
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    while generated_count < count:
        # 如果单词库不够用，创建组合词
        if len(all_words) < count - generated_count:
            for word in TECH_WORDS:
                suffix = ''.join(random.choices(string.ascii_lowercase, k=3))
                all_words.append(f"{word}-{suffix}")
        
        # 选择一个未使用的单词
        while True:
            if not all_words:
                raise ValueError("无法生成足够多的唯一标签名")
            
            tag_name = random.choice(all_words)
            all_words.remove(tag_name)
            
            if tag_name not in used_tags:
                used_tags.add(tag_name)
                break
        
        # 生成slug
        slug = generate_slug(tag_name)
        
        # 判断是否为featured
        is_featured = generated_count in featured_indices
        
        # 插入到数据库
        try:
            # 使用参数化查询
            insert_query = f"INSERT INTO {table_name} (slug, name, featured, created_at, updated_at) VALUES (%s, %s, %s, %s, %s)"
            cursor.execute(insert_query, (slug, tag_name, is_featured, now, now))
            generated_count += 1
            print(f"创建标签: {tag_name}, featured: {is_featured}")
        except psycopg2.errors.UniqueViolation:
            # 如果违反唯一约束，则回滚当前事务并继续
            cursor.connection.rollback()
            print(f"标签 {tag_name} 已存在，跳过")
            continue

def main():
    """主函数"""
    conn = None
    cursor = None
    
    try:
        # 连接到数据库
        conn = psycopg2.connect(**DB_CONFIG)
        conn.autocommit = False
        cursor = conn.cursor()
        
        # 检查表是否存在
        if check_table_exists(cursor, TABLE_NAME):
            print(f"找到 {TABLE_NAME} 表，正在生成数据...")
            # 生成标签
            generate_tags(cursor, count=100, featured_count=30, table_name=TABLE_NAME)
            # 提交事务
            conn.commit()
            print("成功生成标签数据!")
        else:
            print(f"错误: 数据库中不存在 {TABLE_NAME} 表。需要运行数据库迁移。")
            
            # 列出所有可能的表名
            cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';")
            tables = cursor.fetchall()
            if tables:
                print("数据库中的现有表:")
                for table in tables:
                    print(f"- {table[0]}")
            else:
                print("数据库中没有表。")
            
            # 询问是否运行迁移
            answer = input("是否尝试运行数据库迁移? (y/n): ")
            if answer.lower() in ('y', 'yes'):
                success = run_migration()
                if success:
                    # 重新连接数据库并运行生成
                    cursor.close()
                    conn.close()
                    
                    conn = psycopg2.connect(**DB_CONFIG)
                    conn.autocommit = False
                    cursor = conn.cursor()
                    
                    if check_table_exists(cursor, TABLE_NAME):
                        print(f"找到 {TABLE_NAME} 表，现在开始生成数据...")
                        generate_tags(cursor, count=100, featured_count=30, table_name=TABLE_NAME)
                        conn.commit()
                        print("成功生成标签数据!")
                    else:
                        print(f"在迁移后仍然找不到 {TABLE_NAME} 表。请手动检查问题。")
        
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"错误: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    main() 