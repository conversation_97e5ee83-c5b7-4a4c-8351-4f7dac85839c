#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psycopg2
import random
import string
import time
import os
import subprocess
from datetime import datetime

# 分类数据，包含名称、描述和图标
CATEGORIES = [
    {
        "name": "Arts & Entertainment",
        "description": "Discover the world of arts and entertainment, including music, film, theater, and visual arts.",
        "icon": "theater-masks"
    },
    {
        "name": "Autos & Vehicles",
        "description": "Everything related to automobiles, motorcycles, boats, and other vehicles.",
        "icon": "car"
    },
    {
        "name": "Beauty & Fitness",
        "description": "Resources for personal care, beauty products, fitness routines, and wellness.",
        "icon": "spa"
    },
    {
        "name": "Books & Literature",
        "description": "Explore books, literary works, authors, and publishing resources.",
        "icon": "book"
    },
    {
        "name": "Business & Industrial",
        "description": "Information about business operations, industrial equipment, and corporate services.",
        "icon": "briefcase"
    },
    {
        "name": "Computers & Electronics",
        "description": "Everything about computing devices, electronic equipment, and related technologies.",
        "icon": "laptop"
    },
    {
        "name": "Finance",
        "description": "Resources for personal and business finance, banking, investments, and insurance.",
        "icon": "chart-line"
    },
    {
        "name": "Food & Drink",
        "description": "Discover culinary arts, recipes, beverages, restaurants, and food culture.",
        "icon": "utensils"
    },
    {
        "name": "Games",
        "description": "Information about video games, board games, puzzles, and recreational activities.",
        "icon": "gamepad"
    },
    {
        "name": "Health",
        "description": "Resources for medical information, healthcare services, and maintaining well-being.",
        "icon": "heartbeat"
    },
    {
        "name": "Hobbies & Leisure",
        "description": "Explore recreational activities, crafts, collecting, and other leisure pursuits.",
        "icon": "palette"
    },
    {
        "name": "Home & Garden",
        "description": "Information about home improvement, gardening, interior design, and household management.",
        "icon": "home"
    },
    {
        "name": "Internet & Telecom",
        "description": "Resources for internet services, telecommunications, web technologies, and online tools.",
        "icon": "wifi"
    },
    {
        "name": "Jobs & Education",
        "description": "Information about career opportunities, job searching, educational resources, and training.",
        "icon": "graduation-cap"
    },
    {
        "name": "Law & Government",
        "description": "Resources for legal information, government services, public policy, and regulations.",
        "icon": "balance-scale"
    },
    {
        "name": "News",
        "description": "Current events, journalism, news publications, and media coverage.",
        "icon": "newspaper"
    },
    {
        "name": "Online Communities",
        "description": "Social networks, forums, virtual worlds, and other online gathering places.",
        "icon": "users"
    },
    {
        "name": "People & Society",
        "description": "Information about social sciences, demographics, cultural groups, and human behavior.",
        "icon": "user-friends"
    },
    {
        "name": "Pets & Animals",
        "description": "Resources for pet care, wildlife, animal species, and veterinary services.",
        "icon": "paw"
    },
    {
        "name": "Real Estate",
        "description": "Information about property buying, selling, renting, and real estate investments.",
        "icon": "building"
    },
    {
        "name": "Reference",
        "description": "Resources for research, dictionaries, encyclopedias, and educational references.",
        "icon": "book-open"
    },
    {
        "name": "Science",
        "description": "Explore scientific disciplines, research, discoveries, and technological innovations.",
        "icon": "flask"
    },
    {
        "name": "Sports",
        "description": "Information about athletic activities, sporting events, teams, and fitness competitions.",
        "icon": "futbol"
    },
    {
        "name": "Travel & Transportation",
        "description": "Resources for travel planning, destinations, transportation services, and tourism.",
        "icon": "plane"
    },
    {
        "name": "World Localities",
        "description": "Information about geographic regions, countries, cities, and local communities.",
        "icon": "globe"
    },
    {
        "name": "Antiques & Collectibles",
        "description": "Resources for vintage items, collectibles, memorabilia, and antique markets.",
        "icon": "gem"
    },
    {
        "name": "Apparel",
        "description": "Information about clothing, fashion, accessories, and textile products.",
        "icon": "tshirt"
    },
    {
        "name": "Auctions",
        "description": "Resources for auction events, bidding platforms, and collectible sales.",
        "icon": "gavel"
    },
    {
        "name": "Classifieds",
        "description": "Platforms for buying and selling items, services, and opportunities locally.",
        "icon": "tags"
    },
    {
        "name": "Consumer Resources",
        "description": "Information about consumer rights, product reviews, and shopping guides.",
        "icon": "shopping-basket"
    },
    {
        "name": "Discount & Outlet Stores",
        "description": "Resources for finding deals, discounts, and reduced-price merchandise.",
        "icon": "percent"
    },
    {
        "name": "Entertainment Media",
        "description": "Information about movies, music, games, and other entertainment products.",
        "icon": "film"
    },
    {
        "name": "Gifts & Special Event Items",
        "description": "Resources for gift ideas, celebration supplies, and special occasion items.",
        "icon": "gift"
    },
    {
        "name": "Green & Eco-Friendly Shopping",
        "description": "Information about sustainable products, eco-friendly options, and ethical shopping.",
        "icon": "leaf"
    },
    {
        "name": "Luxury Goods",
        "description": "Resources for high-end products, premium brands, and luxury merchandise.",
        "icon": "crown"
    },
    {
        "name": "Mass Merchants & Department Stores",
        "description": "Information about large retailers, department stores, and shopping centers.",
        "icon": "store"
    },
    {
        "name": "Photo & Video Services",
        "description": "Resources for photography, videography, image processing, and visual media.",
        "icon": "camera"
    },
    {
        "name": "Shopping Portals",
        "description": "Online platforms for shopping, product comparison, and e-commerce.",
        "icon": "shopping-cart"
    },
    {
        "name": "Swap Meets & Outdoor Markets",
        "description": "Information about flea markets, farmers markets, and trading events.",
        "icon": "store-alt"
    },
    {
        "name": "Toys",
        "description": "Resources for toys, games, children's products, and recreational items.",
        "icon": "puzzle-piece"
    },
    {
        "name": "Wholesalers & Liquidators",
        "description": "Information about bulk purchasing, wholesale suppliers, and liquidation sales.",
        "icon": "boxes"
    },
    {
        "name": "Others",
        "description": "Miscellaneous categories and topics that don't fit into other classifications.",
        "icon": "ellipsis-h"
    }
]

# 数据库连接信息
DB_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "user": "postgres",
    "password": "postgres",
    "dbname": "brandreviews"
}

# GORM通常将struct名称转换为snake_case复数形式
TABLE_NAME = "categories"  # Category -> categories

def generate_slug(name):
    """根据名称生成slug"""
    return name.lower().replace(" & ", "-").replace(" ", "-")

def check_table_exists(cursor, table_name=TABLE_NAME):
    """检查表是否存在"""
    cursor.execute("""
    SELECT EXISTS (
       SELECT FROM information_schema.tables 
       WHERE table_schema = 'public'
       AND table_name = %s
    );
    """, (table_name,))
    return cursor.fetchone()[0]

def run_migration():
    """运行Go迁移工具创建表"""
    print("尝试运行数据库迁移...")
    try:
        # 找到项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        
        # 构建migrate命令路径
        migrate_cmd = ["go", "run", "cmd/migrate/main.go"]
        
        # 切换到项目根目录并运行
        original_dir = os.getcwd()
        os.chdir(project_root)
        
        result = subprocess.run(migrate_cmd, 
                                stdout=subprocess.PIPE, 
                                stderr=subprocess.PIPE, 
                                text=True)
        
        # 切回原目录
        os.chdir(original_dir)
        
        if result.returncode == 0:
            print("迁移成功完成!")
            return True
        else:
            print(f"迁移失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"运行迁移时出错: {e}")
        return False

def generate_categories(cursor, table_name=TABLE_NAME):
    """生成分类数据"""
    # 当前时间
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 已生成的分类数量
    generated_count = 0
    
    # 随机选择30%的分类作为featured
    featured_count = len(CATEGORIES) // 3
    featured_indices = random.sample(range(len(CATEGORIES)), featured_count)
    
    for i, category in enumerate(CATEGORIES):
        # 生成slug
        slug = generate_slug(category["name"])
        
        # 判断是否为featured
        is_featured = i in featured_indices
        
        # 插入到数据库
        try:
            # 使用参数化查询
            insert_query = f"INSERT INTO {table_name} (slug, name, description, icon, featured, created_at, updated_at) VALUES (%s, %s, %s, %s, %s, %s, %s)"
            cursor.execute(insert_query, (slug, category["name"], category["description"], category["icon"], is_featured, now, now))
            generated_count += 1
            print(f"创建分类: {category['name']}, featured: {is_featured}")
        except psycopg2.errors.UniqueViolation:
            # 如果违反唯一约束，则回滚当前事务并继续
            cursor.connection.rollback()
            print(f"分类 {category['name']} 已存在，跳过")
            continue
    
    return generated_count

def main():
    """主函数"""
    conn = None
    cursor = None
    
    try:
        # 连接到数据库
        conn = psycopg2.connect(**DB_CONFIG)
        conn.autocommit = False
        cursor = conn.cursor()
        
        # 检查表是否存在
        if check_table_exists(cursor, TABLE_NAME):
            print(f"找到 {TABLE_NAME} 表，正在生成数据...")
            # 生成分类
            count = generate_categories(cursor, table_name=TABLE_NAME)
            # 提交事务
            conn.commit()
            print(f"成功生成 {count} 个分类数据!")
        else:
            print(f"错误: 数据库中不存在 {TABLE_NAME} 表。需要运行数据库迁移。")
            
            # 列出所有可能的表名
            cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';")
            tables = cursor.fetchall()
            if tables:
                print("数据库中的现有表:")
                for table in tables:
                    print(f"- {table[0]}")
            else:
                print("数据库中没有表。")
            
            # 询问是否运行迁移
            answer = input("是否尝试运行数据库迁移? (y/n): ")
            if answer.lower() in ('y', 'yes'):
                success = run_migration()
                if success:
                    # 重新连接数据库并运行生成
                    cursor.close()
                    conn.close()
                    
                    conn = psycopg2.connect(**DB_CONFIG)
                    conn.autocommit = False
                    cursor = conn.cursor()
                    
                    if check_table_exists(cursor, TABLE_NAME):
                        print(f"找到 {TABLE_NAME} 表，现在开始生成数据...")
                        count = generate_categories(cursor, table_name=TABLE_NAME)
                        conn.commit()
                        print(f"成功生成 {count} 个分类数据!")
                    else:
                        print(f"在迁移后仍然找不到 {TABLE_NAME} 表。请手动检查问题。")
        
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"错误: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    main()