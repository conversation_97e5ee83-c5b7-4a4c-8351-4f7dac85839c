-- Migration: Optimize entities and add new fields
-- Description: Add new fields and optimize existing entities for performance

-- ============================================================================
-- BRANDS TABLE OPTIMIZATIONS
-- ============================================================================

-- Add affiliate_link field to brands table
ALTER TABLE brands ADD COLUMN IF NOT EXISTS affiliate_link VARCHAR(1000);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_brand_id ON brands(id);
CREATE INDEX IF NOT EXISTS idx_brand_category ON brands(category_id);
CREATE INDEX IF NOT EXISTS idx_brand_created ON brands(created_at);
CREATE INDEX IF NOT EXISTS idx_brand_name ON brands(name);
CREATE INDEX IF NOT EXISTS idx_brand_featured ON brands(featured);
CREATE INDEX IF NOT EXISTS idx_brand_active ON brands(active);
CREATE INDEX IF NOT EXISTS idx_brand_category_slug ON brands(category_slug);

-- Optimize slug index (already exists as unique, but ensure proper naming)
DROP INDEX IF EXISTS brands_slug_key;
CREATE UNIQUE INDEX IF NOT EXISTS idx_brand_slug ON brands(slug);

-- ============================================================================
-- DEALS TABLE OPTIMIZATIONS  
-- ============================================================================

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_deal_id ON deals(id);
CREATE INDEX IF NOT EXISTS idx_deal_brand ON deals(brand_id);
CREATE INDEX IF NOT EXISTS idx_deal_category ON deals(category_id);
CREATE INDEX IF NOT EXISTS idx_deal_start_date ON deals(start_date);
CREATE INDEX IF NOT EXISTS idx_deal_end_date ON deals(end_date);
CREATE INDEX IF NOT EXISTS idx_deal_created ON deals(created_at);
CREATE INDEX IF NOT EXISTS idx_deal_title ON deals(title);
CREATE INDEX IF NOT EXISTS idx_deal_discount_type ON deals(discount_type);
CREATE INDEX IF NOT EXISTS idx_deal_brand_slug ON deals(brand_slug);
CREATE INDEX IF NOT EXISTS idx_deal_category_slug ON deals(category_slug);
CREATE INDEX IF NOT EXISTS idx_deal_featured ON deals(featured);
CREATE INDEX IF NOT EXISTS idx_deal_active ON deals(active);
CREATE INDEX IF NOT EXISTS idx_deal_verified ON deals(verified);

-- Optimize slug index
DROP INDEX IF EXISTS deals_slug_key;
CREATE UNIQUE INDEX IF NOT EXISTS idx_deal_slug ON deals(slug);

-- Update decimal precision for better accuracy
ALTER TABLE deals ALTER COLUMN original_price TYPE DECIMAL(12,2);
ALTER TABLE deals ALTER COLUMN sale_price TYPE DECIMAL(12,2);
ALTER TABLE deals ALTER COLUMN discount_value TYPE DECIMAL(12,2);

-- ============================================================================
-- COUPONS TABLE OPTIMIZATIONS
-- ============================================================================

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_coupon_id ON coupons(id);
CREATE INDEX IF NOT EXISTS idx_coupon_brand ON coupons(brand_id);
CREATE INDEX IF NOT EXISTS idx_coupon_category ON coupons(category_id);
CREATE INDEX IF NOT EXISTS idx_coupon_start_date ON coupons(start_date);
CREATE INDEX IF NOT EXISTS idx_coupon_end_date ON coupons(end_date);
CREATE INDEX IF NOT EXISTS idx_coupon_created ON coupons(created_at);
CREATE INDEX IF NOT EXISTS idx_coupon_title ON coupons(title);
CREATE INDEX IF NOT EXISTS idx_coupon_discount_type ON coupons(discount_type);
CREATE INDEX IF NOT EXISTS idx_coupon_brand_slug ON coupons(brand_slug);
CREATE INDEX IF NOT EXISTS idx_coupon_category_slug ON coupons(category_slug);
CREATE INDEX IF NOT EXISTS idx_coupon_featured ON coupons(featured);
CREATE INDEX IF NOT EXISTS idx_coupon_active ON coupons(active);
CREATE INDEX IF NOT EXISTS idx_coupon_verified ON coupons(verified);

-- Optimize slug and code indexes
DROP INDEX IF EXISTS coupons_slug_key;
DROP INDEX IF EXISTS coupons_code_key;
CREATE UNIQUE INDEX IF NOT EXISTS idx_coupon_slug ON coupons(slug);
CREATE UNIQUE INDEX IF NOT EXISTS idx_coupon_code ON coupons(code);

-- Update decimal precision for better accuracy
ALTER TABLE coupons ALTER COLUMN discount_value TYPE DECIMAL(12,2);
ALTER TABLE coupons ALTER COLUMN min_order_value TYPE DECIMAL(12,2);
ALTER TABLE coupons ALTER COLUMN max_discount TYPE DECIMAL(12,2);

-- ============================================================================
-- ARTICLES TABLE OPTIMIZATIONS
-- ============================================================================

-- Add new metadata fields
ALTER TABLE articles ADD COLUMN IF NOT EXISTS view_count BIGINT DEFAULT 0;
ALTER TABLE articles ADD COLUMN IF NOT EXISTS last_viewed_at TIMESTAMP;
ALTER TABLE articles ADD COLUMN IF NOT EXISTS meta_title VARCHAR(70);
ALTER TABLE articles ADD COLUMN IF NOT EXISTS meta_description VARCHAR(160);
ALTER TABLE articles ADD COLUMN IF NOT EXISTS author_name VARCHAR(100);
ALTER TABLE articles ADD COLUMN IF NOT EXISTS last_updated_by VARCHAR(100);
ALTER TABLE articles ADD COLUMN IF NOT EXISTS published BOOLEAN DEFAULT false;
ALTER TABLE articles ADD COLUMN IF NOT EXISTS trending BOOLEAN DEFAULT false;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_article_id ON articles(id);
CREATE INDEX IF NOT EXISTS idx_article_brand ON articles(brand_id);
CREATE INDEX IF NOT EXISTS idx_article_category ON articles(category_id);
CREATE INDEX IF NOT EXISTS idx_article_published ON articles(published_at);
CREATE INDEX IF NOT EXISTS idx_article_views ON articles(view_count);
CREATE INDEX IF NOT EXISTS idx_article_created ON articles(created_at);
CREATE INDEX IF NOT EXISTS idx_article_last_viewed ON articles(last_viewed_at);
CREATE INDEX IF NOT EXISTS idx_article_title ON articles(title);
CREATE INDEX IF NOT EXISTS idx_article_brand_slug ON articles(brand_slug);
CREATE INDEX IF NOT EXISTS idx_article_category_slug ON articles(category_slug);
CREATE INDEX IF NOT EXISTS idx_article_featured ON articles(featured);
CREATE INDEX IF NOT EXISTS idx_article_published_status ON articles(published);
CREATE INDEX IF NOT EXISTS idx_article_trending ON articles(trending);

-- Optimize slug index
DROP INDEX IF EXISTS articles_slug_key;
CREATE UNIQUE INDEX IF NOT EXISTS idx_article_slug ON articles(slug);

-- Update array columns to use proper types
ALTER TABLE articles ALTER COLUMN deal_ids TYPE BIGINT[];
ALTER TABLE articles ALTER COLUMN tag_ids TYPE BIGINT[];

-- ============================================================================
-- CATEGORIES TABLE OPTIMIZATIONS (if not already optimized)
-- ============================================================================

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_category_id ON categories(id);
CREATE INDEX IF NOT EXISTS idx_category_created ON categories(created_at);
CREATE INDEX IF NOT EXISTS idx_category_name ON categories(name);
CREATE INDEX IF NOT EXISTS idx_category_featured ON categories(featured);
CREATE INDEX IF NOT EXISTS idx_category_active ON categories(active);

-- Optimize slug index
DROP INDEX IF EXISTS categories_slug_key;
CREATE UNIQUE INDEX IF NOT EXISTS idx_category_slug ON categories(slug);

-- ============================================================================
-- TAGS TABLE OPTIMIZATIONS (if not already optimized)
-- ============================================================================

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tag_id ON tags(id);
CREATE INDEX IF NOT EXISTS idx_tag_created ON tags(created_at);
CREATE INDEX IF NOT EXISTS idx_tag_name ON tags(name);
CREATE INDEX IF NOT EXISTS idx_tag_featured ON tags(featured);
CREATE INDEX IF NOT EXISTS idx_tag_active ON tags(active);

-- Optimize slug index
DROP INDEX IF EXISTS tags_slug_key;
CREATE UNIQUE INDEX IF NOT EXISTS idx_tag_slug ON tags(slug);

-- ============================================================================
-- COMPOSITE INDEXES FOR COMMON QUERIES
-- ============================================================================

-- Brands: Active featured brands by category
CREATE INDEX IF NOT EXISTS idx_brand_active_featured_category ON brands(active, featured, category_id) WHERE active = true;

-- Deals: Active valid deals by brand and category
CREATE INDEX IF NOT EXISTS idx_deal_active_valid_brand ON deals(active, brand_id, start_date, end_date) WHERE active = true;
CREATE INDEX IF NOT EXISTS idx_deal_active_valid_category ON deals(active, category_id, start_date, end_date) WHERE active = true;

-- Coupons: Active valid coupons by brand and category
CREATE INDEX IF NOT EXISTS idx_coupon_active_valid_brand ON coupons(active, brand_id, start_date, end_date) WHERE active = true;
CREATE INDEX IF NOT EXISTS idx_coupon_active_valid_category ON coupons(active, category_id, start_date, end_date) WHERE active = true;

-- Articles: Published articles by brand and category
CREATE INDEX IF NOT EXISTS idx_article_published_brand ON articles(published, brand_id, published_at) WHERE published = true;
CREATE INDEX IF NOT EXISTS idx_article_published_category ON articles(published, category_id, published_at) WHERE published = true;

-- ============================================================================
-- PERFORMANCE OPTIMIZATIONS
-- ============================================================================

-- Update table statistics for better query planning
ANALYZE brands;
ANALYZE deals;
ANALYZE coupons;
ANALYZE articles;
ANALYZE categories;
ANALYZE tags;

-- Set appropriate fill factors for tables with frequent updates
ALTER TABLE brands SET (fillfactor = 90);
ALTER TABLE deals SET (fillfactor = 85);
ALTER TABLE coupons SET (fillfactor = 85);
ALTER TABLE articles SET (fillfactor = 80);

-- ============================================================================
-- CONSTRAINTS AND VALIDATIONS
-- ============================================================================

-- Add check constraints for data integrity
ALTER TABLE deals ADD CONSTRAINT chk_deal_prices CHECK (
    original_price >= 0 AND 
    sale_price >= 0 AND 
    discount_value >= 0 AND
    (original_price = 0 OR sale_price <= original_price)
);

ALTER TABLE coupons ADD CONSTRAINT chk_coupon_values CHECK (
    discount_value >= 0 AND 
    min_order_value >= 0 AND 
    max_discount >= 0 AND
    usage_limit >= 0 AND
    used_count >= 0 AND
    user_usage_limit >= 1
);

ALTER TABLE articles ADD CONSTRAINT chk_article_view_count CHECK (view_count >= 0);

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON COLUMN brands.affiliate_link IS 'Affiliate marketing URL for the brand';
COMMENT ON COLUMN articles.view_count IS 'Number of times the article has been viewed';
COMMENT ON COLUMN articles.last_viewed_at IS 'Timestamp of the last view';
COMMENT ON COLUMN articles.meta_title IS 'SEO meta title (max 70 chars)';
COMMENT ON COLUMN articles.meta_description IS 'SEO meta description (max 160 chars)';
COMMENT ON COLUMN articles.published IS 'Whether the article is published and visible';
COMMENT ON COLUMN articles.trending IS 'Whether the article is marked as trending';

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- Log migration completion
INSERT INTO schema_migrations (version, applied_at) 
VALUES ('001_optimize_entities', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW();
