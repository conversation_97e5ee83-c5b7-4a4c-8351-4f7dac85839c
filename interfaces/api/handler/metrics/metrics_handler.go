package metrics

import (
	"net/http"
	"runtime"
	"time"

	"brandreviews/infra/cache"
	"brandreviews/interfaces/api/middleware"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// MetricsHandler handles metrics and monitoring endpoints
type MetricsHandler struct {
	db           *gorm.DB
	cacheManager *cache.CacheManager
	tracer       *middleware.RequestTracer
	logger       *zap.Logger
	startTime    time.Time
}

// NewMetricsHandler creates a new metrics handler
func NewMetricsHandler(
	db *gorm.DB,
	cacheManager *cache.CacheManager,
	tracer *middleware.RequestTracer,
	logger *zap.Logger,
) *MetricsHandler {
	return &MetricsHandler{
		db:           db,
		cacheManager: cacheManager,
		tracer:       tracer,
		logger:       logger,
		startTime:    time.Now(),
	}
}

// SystemMetrics represents system-level metrics
type SystemMetrics struct {
	Timestamp    time.Time              `json:"timestamp"`
	Uptime       string                 `json:"uptime"`
	Version      string                 `json:"version"`
	GoVersion    string                 `json:"go_version"`
	Memory       MemoryMetrics          `json:"memory"`
	Goroutines   int                    `json:"goroutines"`
	Database     DatabaseMetrics        `json:"database"`
	Cache        map[string]interface{} `json:"cache"`
	Requests     map[string]interface{} `json:"requests"`
	Performance  PerformanceMetrics     `json:"performance"`
}

// MemoryMetrics represents memory usage metrics
type MemoryMetrics struct {
	AllocMB        uint64  `json:"alloc_mb"`
	TotalAllocMB   uint64  `json:"total_alloc_mb"`
	SysMB          uint64  `json:"sys_mb"`
	NumGC          uint32  `json:"num_gc"`
	GCCPUFraction  float64 `json:"gc_cpu_fraction"`
	HeapAllocMB    uint64  `json:"heap_alloc_mb"`
	HeapSysMB      uint64  `json:"heap_sys_mb"`
	HeapIdleMB     uint64  `json:"heap_idle_mb"`
	HeapInUseMB    uint64  `json:"heap_inuse_mb"`
	HeapReleasedMB uint64  `json:"heap_released_mb"`
	HeapObjects    uint64  `json:"heap_objects"`
}

// DatabaseMetrics represents database connection metrics
type DatabaseMetrics struct {
	OpenConnections     int           `json:"open_connections"`
	InUse              int           `json:"in_use"`
	Idle               int           `json:"idle"`
	WaitCount          int64         `json:"wait_count"`
	WaitDuration       string        `json:"wait_duration"`
	MaxIdleClosed      int64         `json:"max_idle_closed"`
	MaxIdleTimeClosed  int64         `json:"max_idle_time_closed"`
	MaxLifetimeClosed  int64         `json:"max_lifetime_closed"`
}

// PerformanceMetrics represents application performance metrics
type PerformanceMetrics struct {
	AverageResponseTime string  `json:"average_response_time"`
	P95ResponseTime     string  `json:"p95_response_time"`
	P99ResponseTime     string  `json:"p99_response_time"`
	ErrorRate          float64 `json:"error_rate"`
	RequestsPerSecond  float64 `json:"requests_per_second"`
	CacheHitRatio      float64 `json:"cache_hit_ratio"`
}

// GetMetrics returns comprehensive system metrics
func (h *MetricsHandler) GetMetrics(c *gin.Context) {
	metrics := h.collectSystemMetrics()
	c.JSON(http.StatusOK, metrics)
}

// GetRequestMetrics returns request-specific metrics
func (h *MetricsHandler) GetRequestMetrics(c *gin.Context) {
	if h.tracer == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Request tracer not available",
		})
		return
	}
	
	metrics := h.tracer.GetMetrics()
	c.JSON(http.StatusOK, metrics)
}

// GetCacheMetrics returns cache-specific metrics
func (h *MetricsHandler) GetCacheMetrics(c *gin.Context) {
	if h.cacheManager == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Cache manager not available",
		})
		return
	}
	
	metrics := h.cacheManager.GetStats()
	c.JSON(http.StatusOK, metrics)
}

// GetDatabaseMetrics returns database-specific metrics
func (h *MetricsHandler) GetDatabaseMetrics(c *gin.Context) {
	if h.db == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Database not available",
		})
		return
	}
	
	sqlDB, err := h.db.DB()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get database instance",
		})
		return
	}
	
	stats := sqlDB.Stats()
	metrics := DatabaseMetrics{
		OpenConnections:    stats.OpenConnections,
		InUse:             stats.InUse,
		Idle:              stats.Idle,
		WaitCount:         stats.WaitCount,
		WaitDuration:      stats.WaitDuration.String(),
		MaxIdleClosed:     stats.MaxIdleClosed,
		MaxIdleTimeClosed: stats.MaxIdleTimeClosed,
		MaxLifetimeClosed: stats.MaxLifetimeClosed,
	}
	
	c.JSON(http.StatusOK, metrics)
}

// ResetMetrics resets all metrics (useful for testing)
func (h *MetricsHandler) ResetMetrics(c *gin.Context) {
	if h.tracer != nil {
		h.tracer.ResetMetrics()
	}
	
	c.JSON(http.StatusOK, gin.H{
		"message": "Metrics reset successfully",
		"timestamp": time.Now(),
	})
}

// collectSystemMetrics collects comprehensive system metrics
func (h *MetricsHandler) collectSystemMetrics() SystemMetrics {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	metrics := SystemMetrics{
		Timestamp: time.Now(),
		Uptime:    time.Since(h.startTime).String(),
		Version:   "1.0.0", // Should come from build info
		GoVersion: runtime.Version(),
		Memory: MemoryMetrics{
			AllocMB:        bToMb(m.Alloc),
			TotalAllocMB:   bToMb(m.TotalAlloc),
			SysMB:          bToMb(m.Sys),
			NumGC:          m.NumGC,
			GCCPUFraction:  m.GCCPUFraction,
			HeapAllocMB:    bToMb(m.HeapAlloc),
			HeapSysMB:      bToMb(m.HeapSys),
			HeapIdleMB:     bToMb(m.HeapIdle),
			HeapInUseMB:    bToMb(m.HeapInuse),
			HeapReleasedMB: bToMb(m.HeapReleased),
			HeapObjects:    m.HeapObjects,
		},
		Goroutines: runtime.NumGoroutine(),
		Database:   h.getDatabaseMetrics(),
		Performance: h.getPerformanceMetrics(),
	}
	
	// Add cache metrics if available
	if h.cacheManager != nil {
		metrics.Cache = h.cacheManager.GetStats()
	}
	
	// Add request metrics if available
	if h.tracer != nil {
		metrics.Requests = h.tracer.GetMetrics()
	}
	
	return metrics
}

// getDatabaseMetrics collects database metrics
func (h *MetricsHandler) getDatabaseMetrics() DatabaseMetrics {
	if h.db == nil {
		return DatabaseMetrics{}
	}
	
	sqlDB, err := h.db.DB()
	if err != nil {
		return DatabaseMetrics{}
	}
	
	stats := sqlDB.Stats()
	return DatabaseMetrics{
		OpenConnections:    stats.OpenConnections,
		InUse:             stats.InUse,
		Idle:              stats.Idle,
		WaitCount:         stats.WaitCount,
		WaitDuration:      stats.WaitDuration.String(),
		MaxIdleClosed:     stats.MaxIdleClosed,
		MaxIdleTimeClosed: stats.MaxIdleTimeClosed,
		MaxLifetimeClosed: stats.MaxLifetimeClosed,
	}
}

// getPerformanceMetrics calculates performance metrics
func (h *MetricsHandler) getPerformanceMetrics() PerformanceMetrics {
	perf := PerformanceMetrics{}
	
	if h.tracer != nil {
		requestMetrics := h.tracer.GetMetrics()
		
		// Extract performance data from request metrics
		if avgTime, ok := requestMetrics["avg_response_time"].(string); ok {
			perf.AverageResponseTime = avgTime
		}
		
		if errorRate, ok := requestMetrics["error_rate"].(float64); ok {
			perf.ErrorRate = errorRate
		}
		
		// Calculate requests per second based on uptime and total requests
		if totalRequests, ok := requestMetrics["total_requests"].(int64); ok {
			uptime := time.Since(h.startTime).Seconds()
			if uptime > 0 {
				perf.RequestsPerSecond = float64(totalRequests) / uptime
			}
		}
	}
	
	if h.cacheManager != nil {
		cacheStats := h.cacheManager.GetStats()
		if hitRatio, ok := cacheStats["total_hit_ratio"].(float64); ok {
			perf.CacheHitRatio = hitRatio
		}
	}
	
	return perf
}

// PrometheusMetrics returns metrics in Prometheus format
func (h *MetricsHandler) PrometheusMetrics(c *gin.Context) {
	c.Header("Content-Type", "text/plain; version=0.0.4; charset=utf-8")
	
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	// Basic system metrics
	c.String(http.StatusOK, "# HELP go_memstats_alloc_bytes Number of bytes allocated and still in use.\n")
	c.String(http.StatusOK, "# TYPE go_memstats_alloc_bytes gauge\n")
	c.String(http.StatusOK, "go_memstats_alloc_bytes %d\n", m.Alloc)
	
	c.String(http.StatusOK, "# HELP go_memstats_sys_bytes Number of bytes obtained from system.\n")
	c.String(http.StatusOK, "# TYPE go_memstats_sys_bytes gauge\n")
	c.String(http.StatusOK, "go_memstats_sys_bytes %d\n", m.Sys)
	
	c.String(http.StatusOK, "# HELP go_goroutines Number of goroutines that currently exist.\n")
	c.String(http.StatusOK, "# TYPE go_goroutines gauge\n")
	c.String(http.StatusOK, "go_goroutines %d\n", runtime.NumGoroutine())
	
	// Request metrics
	if h.tracer != nil {
		requestMetrics := h.tracer.GetMetrics()
		
		if totalRequests, ok := requestMetrics["total_requests"].(int64); ok {
			c.String(http.StatusOK, "# HELP http_requests_total Total number of HTTP requests.\n")
			c.String(http.StatusOK, "# TYPE http_requests_total counter\n")
			c.String(http.StatusOK, "http_requests_total %d\n", totalRequests)
		}
		
		if totalErrors, ok := requestMetrics["total_errors"].(int64); ok {
			c.String(http.StatusOK, "# HELP http_requests_errors_total Total number of HTTP request errors.\n")
			c.String(http.StatusOK, "# TYPE http_requests_errors_total counter\n")
			c.String(http.StatusOK, "http_requests_errors_total %d\n", totalErrors)
		}
	}
	
	// Cache metrics
	if h.cacheManager != nil {
		cacheStats := h.cacheManager.GetStats()
		
		if l1Stats, ok := cacheStats["l1_cache"].(map[string]interface{}); ok {
			if hits, ok := l1Stats["hits"].(int64); ok {
				c.String(http.StatusOK, "# HELP cache_hits_total Total number of cache hits.\n")
				c.String(http.StatusOK, "# TYPE cache_hits_total counter\n")
				c.String(http.StatusOK, "cache_hits_total{level=\"l1\"} %d\n", hits)
			}
			
			if misses, ok := l1Stats["misses"].(int64); ok {
				c.String(http.StatusOK, "# HELP cache_misses_total Total number of cache misses.\n")
				c.String(http.StatusOK, "# TYPE cache_misses_total counter\n")
				c.String(http.StatusOK, "cache_misses_total{level=\"l1\"} %d\n", misses)
			}
		}
	}
	
	// Database metrics
	if h.db != nil {
		if sqlDB, err := h.db.DB(); err == nil {
			stats := sqlDB.Stats()
			
			c.String(http.StatusOK, "# HELP db_connections_open Number of open database connections.\n")
			c.String(http.StatusOK, "# TYPE db_connections_open gauge\n")
			c.String(http.StatusOK, "db_connections_open %d\n", stats.OpenConnections)
			
			c.String(http.StatusOK, "# HELP db_connections_in_use Number of database connections in use.\n")
			c.String(http.StatusOK, "# TYPE db_connections_in_use gauge\n")
			c.String(http.StatusOK, "db_connections_in_use %d\n", stats.InUse)
		}
	}
}

// bToMb converts bytes to megabytes
func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}
