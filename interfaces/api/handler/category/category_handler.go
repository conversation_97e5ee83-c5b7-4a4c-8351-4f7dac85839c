package category

import (
	"brandreviews/application/category/appservice"
	"brandreviews/application/category/dto"
	"brandreviews/infra/constant"
	"brandreviews/infra/ecode"
	"brandreviews/infra/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)

type CategoryHandler struct {
	categoryApp appservice.CategoryAppService
	logger      *zap.Logger
}

func NewCategoryHandler(categoryApp appservice.CategoryAppService, logger *zap.Logger) *CategoryHandler {
	return &CategoryHandler{
		categoryApp: categoryApp,
		logger:      logger,
	}
}

func (h *CategoryHandler) GetCategoryById(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	category, errc := h.categoryApp.GetCategoryDetailById(c, id)
	if errc != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, category)
	return
}

func (h *CategoryHandler) GetCategoryList(c *gin.Context) {
	var req dto.GetCategoryListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	// 设置默认值和参数验证
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}
	resp, err := h.categoryApp.GetCategoryListByCondition(c, &req)
	if err != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, resp)
	return
}
