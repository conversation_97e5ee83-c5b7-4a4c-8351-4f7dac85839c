package health

import (
	"context"
	"database/sql"
	"net/http"
	"runtime"
	"time"

	"brandreviews/infra/cache"
	"brandreviews/interfaces/api/middleware"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// HealthHandler handles health check endpoints
type HealthHandler struct {
	db           *gorm.DB
	cacheManager *cache.CacheManager
	tracer       *middleware.RequestTracer
	logger       *zap.Logger
	startTime    time.Time
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(
	db *gorm.DB,
	cacheManager *cache.CacheManager,
	tracer *middleware.RequestTracer,
	logger *zap.Logger,
) *HealthHandler {
	return &HealthHandler{
		db:           db,
		cacheManager: cacheManager,
		tracer:       tracer,
		logger:       logger,
		startTime:    time.Now(),
	}
}

// HealthStatus represents the health status of a component
type HealthStatus struct {
	Status    string                 `json:"status"`
	Timestamp time.Time              `json:"timestamp"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Error     string                 `json:"error,omitempty"`
}

// OverallHealth represents the overall health of the application
type OverallHealth struct {
	Status     string                    `json:"status"`
	Timestamp  time.Time                 `json:"timestamp"`
	Version    string                    `json:"version"`
	Uptime     string                    `json:"uptime"`
	Components map[string]*HealthStatus  `json:"components"`
	Metrics    map[string]interface{}    `json:"metrics,omitempty"`
}

// HealthCheck performs a basic health check
func (h *HealthHandler) HealthCheck(c *gin.Context) {
	health := &OverallHealth{
		Status:     "UP",
		Timestamp:  time.Now(),
		Version:    "1.0.0", // This should come from build info
		Uptime:     time.Since(h.startTime).String(),
		Components: make(map[string]*HealthStatus),
	}
	
	// Check database health
	dbHealth := h.checkDatabase()
	health.Components["database"] = dbHealth
	
	// Check cache health
	cacheHealth := h.checkCache()
	health.Components["cache"] = cacheHealth
	
	// Check memory health
	memoryHealth := h.checkMemory()
	health.Components["memory"] = memoryHealth
	
	// Determine overall status
	overallStatus := "UP"
	for _, component := range health.Components {
		if component.Status != "UP" {
			overallStatus = "DOWN"
			break
		}
	}
	health.Status = overallStatus
	
	// Set appropriate HTTP status
	statusCode := http.StatusOK
	if overallStatus != "UP" {
		statusCode = http.StatusServiceUnavailable
	}
	
	c.JSON(statusCode, health)
}

// DetailedHealthCheck performs a detailed health check with metrics
func (h *HealthHandler) DetailedHealthCheck(c *gin.Context) {
	health := &OverallHealth{
		Status:     "UP",
		Timestamp:  time.Now(),
		Version:    "1.0.0",
		Uptime:     time.Since(h.startTime).String(),
		Components: make(map[string]*HealthStatus),
		Metrics:    make(map[string]interface{}),
	}
	
	// Check all components
	health.Components["database"] = h.checkDatabaseDetailed()
	health.Components["cache"] = h.checkCacheDetailed()
	health.Components["memory"] = h.checkMemoryDetailed()
	health.Components["goroutines"] = h.checkGoroutines()
	
	// Add request metrics if tracer is available
	if h.tracer != nil {
		health.Metrics["requests"] = h.tracer.GetMetrics()
	}
	
	// Add cache metrics if cache manager is available
	if h.cacheManager != nil {
		health.Metrics["cache"] = h.cacheManager.GetStats()
	}
	
	// Determine overall status
	overallStatus := "UP"
	for _, component := range health.Components {
		if component.Status != "UP" {
			overallStatus = "DOWN"
			break
		}
	}
	health.Status = overallStatus
	
	// Set appropriate HTTP status
	statusCode := http.StatusOK
	if overallStatus != "UP" {
		statusCode = http.StatusServiceUnavailable
	}
	
	c.JSON(statusCode, health)
}

// ReadinessCheck checks if the application is ready to serve requests
func (h *HealthHandler) ReadinessCheck(c *gin.Context) {
	ready := true
	components := make(map[string]*HealthStatus)
	
	// Check critical components for readiness
	dbHealth := h.checkDatabase()
	components["database"] = dbHealth
	if dbHealth.Status != "UP" {
		ready = false
	}
	
	response := map[string]interface{}{
		"ready":      ready,
		"timestamp":  time.Now(),
		"components": components,
	}
	
	statusCode := http.StatusOK
	if !ready {
		statusCode = http.StatusServiceUnavailable
	}
	
	c.JSON(statusCode, response)
}

// LivenessCheck checks if the application is alive
func (h *HealthHandler) LivenessCheck(c *gin.Context) {
	c.JSON(http.StatusOK, map[string]interface{}{
		"alive":     true,
		"timestamp": time.Now(),
		"uptime":    time.Since(h.startTime).String(),
	})
}

// checkDatabase checks database connectivity
func (h *HealthHandler) checkDatabase() *HealthStatus {
	status := &HealthStatus{
		Timestamp: time.Now(),
	}
	
	if h.db == nil {
		status.Status = "DOWN"
		status.Error = "Database connection not available"
		return status
	}
	
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	sqlDB, err := h.db.DB()
	if err != nil {
		status.Status = "DOWN"
		status.Error = err.Error()
		return status
	}
	
	if err := sqlDB.PingContext(ctx); err != nil {
		status.Status = "DOWN"
		status.Error = err.Error()
		return status
	}
	
	status.Status = "UP"
	return status
}

// checkDatabaseDetailed checks database with detailed metrics
func (h *HealthHandler) checkDatabaseDetailed() *HealthStatus {
	status := h.checkDatabase()
	
	if h.db != nil {
		sqlDB, err := h.db.DB()
		if err == nil {
			stats := sqlDB.Stats()
			status.Details = map[string]interface{}{
				"open_connections":     stats.OpenConnections,
				"in_use":              stats.InUse,
				"idle":                stats.Idle,
				"wait_count":          stats.WaitCount,
				"wait_duration":       stats.WaitDuration.String(),
				"max_idle_closed":     stats.MaxIdleClosed,
				"max_idle_time_closed": stats.MaxIdleTimeClosed,
				"max_lifetime_closed": stats.MaxLifetimeClosed,
			}
		}
	}
	
	return status
}

// checkCache checks cache health
func (h *HealthHandler) checkCache() *HealthStatus {
	status := &HealthStatus{
		Timestamp: time.Now(),
	}
	
	if h.cacheManager == nil {
		status.Status = "DOWN"
		status.Error = "Cache manager not available"
		return status
	}
	
	// Test cache by setting and getting a test value
	testKey := "health_check_test"
	testValue := "test_value"
	
	h.cacheManager.SetL1Only(testKey, testValue, 1*time.Second)
	
	if value, exists := h.cacheManager.Get(testKey); !exists || value != testValue {
		status.Status = "DOWN"
		status.Error = "Cache read/write test failed"
		return status
	}
	
	// Clean up test key
	h.cacheManager.Delete(testKey)
	
	status.Status = "UP"
	return status
}

// checkCacheDetailed checks cache with detailed metrics
func (h *HealthHandler) checkCacheDetailed() *HealthStatus {
	status := h.checkCache()
	
	if h.cacheManager != nil {
		status.Details = h.cacheManager.GetStats()
	}
	
	return status
}

// checkMemory checks memory usage
func (h *HealthHandler) checkMemory() *HealthStatus {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	status := &HealthStatus{
		Status:    "UP",
		Timestamp: time.Now(),
	}
	
	// Check if memory usage is too high (>90% of allocated)
	if m.Sys > 0 && float64(m.Alloc)/float64(m.Sys) > 0.9 {
		status.Status = "WARN"
		status.Error = "High memory usage detected"
	}
	
	return status
}

// checkMemoryDetailed checks memory with detailed metrics
func (h *HealthHandler) checkMemoryDetailed() *HealthStatus {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	status := &HealthStatus{
		Status:    "UP",
		Timestamp: time.Now(),
		Details: map[string]interface{}{
			"alloc_mb":        bToMb(m.Alloc),
			"total_alloc_mb":  bToMb(m.TotalAlloc),
			"sys_mb":          bToMb(m.Sys),
			"num_gc":          m.NumGC,
			"gc_cpu_fraction": m.GCCPUFraction,
			"heap_alloc_mb":   bToMb(m.HeapAlloc),
			"heap_sys_mb":     bToMb(m.HeapSys),
			"heap_idle_mb":    bToMb(m.HeapIdle),
			"heap_inuse_mb":   bToMb(m.HeapInuse),
			"heap_released_mb": bToMb(m.HeapReleased),
			"heap_objects":    m.HeapObjects,
		},
	}
	
	// Check if memory usage is too high
	if m.Sys > 0 && float64(m.Alloc)/float64(m.Sys) > 0.9 {
		status.Status = "WARN"
		status.Error = "High memory usage detected"
	}
	
	return status
}

// checkGoroutines checks goroutine count
func (h *HealthHandler) checkGoroutines() *HealthStatus {
	numGoroutines := runtime.NumGoroutine()
	
	status := &HealthStatus{
		Status:    "UP",
		Timestamp: time.Now(),
		Details: map[string]interface{}{
			"count": numGoroutines,
		},
	}
	
	// Warn if too many goroutines (potential leak)
	if numGoroutines > 1000 {
		status.Status = "WARN"
		status.Error = "High goroutine count detected"
	}
	
	return status
}

// bToMb converts bytes to megabytes
func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}
