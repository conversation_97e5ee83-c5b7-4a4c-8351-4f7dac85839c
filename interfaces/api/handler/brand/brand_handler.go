package brand

import (
	"brandreviews/application/brand/appservice"
	"brandreviews/application/brand/dto"
	"brandreviews/infra/constant"
	"brandreviews/infra/ecode"
	"brandreviews/infra/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)

type BrandHandler struct {
	brandApp appservice.BrandAppService
	logger   *zap.Logger
}

func NewBrandHandler(brandApp appservice.BrandAppService, logger *zap.Logger) *BrandHandler {
	return &BrandHandler{
		brandApp: brandApp,
		logger:   logger,
	}
}

func (h *BrandHandler) GetBrandById(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	brand, errc := h.brandApp.GetBrandDetailById(c, id)
	if errc != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, brand)
	return
}

func (h *BrandHandler) GetBrandBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	brand, errc := h.brandApp.GetBrandDetailBySlug(c, slug)
	if errc != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, brand)
	return
}

func (h *BrandHandler) GetBrandList(c *gin.Context) {
	var req dto.GetBrandListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	// 设置默认值和参数验证
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}
	resp, err := h.brandApp.GetBrandListByCondition(c, &req)
	if err != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, resp)
	return
}
