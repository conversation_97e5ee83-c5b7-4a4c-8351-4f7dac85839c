package coupon

import (
	"brandreviews/application/coupon/appservice"
	"brandreviews/application/coupon/dto"
	"brandreviews/infra/constant"
	"brandreviews/infra/ecode"
	"brandreviews/infra/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)

type CouponHandler struct {
	couponApp appservice.CouponAppService
	logger    *zap.Logger
}

func NewCouponHandler(couponApp appservice.CouponAppService, logger *zap.Logger) *CouponHandler {
	return &CouponHandler{
		couponApp: couponApp,
		logger:    logger,
	}
}

func (h *CouponHandler) GetCouponById(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	coupon, errc := h.couponApp.GetCouponDetailById(c, id)
	if errc != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, coupon)
	return
}

func (h *CouponHandler) GetCouponBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	coupon, errc := h.couponApp.GetCouponDetailBySlug(c, slug)
	if errc != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, coupon)
	return
}

func (h *CouponHandler) GetCouponByCode(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	coupon, errc := h.couponApp.GetCouponDetailByCode(c, code)
	if errc != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, coupon)
	return
}

func (h *CouponHandler) GetCouponList(c *gin.Context) {
	var req dto.GetCouponListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	// 设置默认值和参数验证
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}
	resp, err := h.couponApp.GetCouponListByCondition(c, &req)
	if err != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, resp)
	return
}
