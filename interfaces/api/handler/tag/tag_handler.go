package tag

import (
	"brandreviews/application/tag/appservice"
	"brandreviews/application/tag/dto"
	"brandreviews/infra/constant"
	"brandreviews/infra/ecode"
	"brandreviews/infra/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)

type TagHandler struct {
	tagApp appservice.TagAppService
	logger *zap.Logger
}

func NewTagHandler(tagApp appservice.TagAppService, logger *zap.Logger) *TagHandler {
	return &TagHandler{
		tagApp: tagApp,
		logger: logger,
	}
}

func (h *TagHandler) GetTagById(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	tag, errc := h.tagApp.GetTagDetailById(c, id)
	if errc != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, tag)
	return
}

func (h *TagHandler) GetTagList(c *gin.Context) {
	var req dto.GetTagListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	// 设置默认值和参数验证
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}
	resp, err := h.tagApp.GetTagListByCondition(c, &req)
	if err != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, resp)
	return
}
