package deal

import (
	"brandreviews/application/deal/appservice"
	"brandreviews/application/deal/dto"
	"brandreviews/infra/constant"
	"brandreviews/infra/ecode"
	"brandreviews/infra/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)

type DealHandler struct {
	dealApp appservice.DealAppService
	logger  *zap.Logger
}

func NewDealHandler(dealApp appservice.DealAppService, logger *zap.Logger) *DealHandler {
	return &DealHandler{
		dealApp: dealApp,
		logger:  logger,
	}
}

func (h *DealHandler) GetDealById(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	deal, errc := h.dealApp.GetDealDetailById(c, id)
	if errc != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, deal)
	return
}

func (h *DealHandler) GetDealBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	deal, errc := h.dealApp.GetDealDetailBySlug(c, slug)
	if errc != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, deal)
	return
}

func (h *DealHandler) GetDealList(c *gin.Context) {
	var req dto.GetDealListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}
	// 设置默认值和参数验证
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}
	resp, err := h.dealApp.GetDealListByCondition(c, &req)
	if err != nil {
		response.Error(c, ecode.ErrNotFound.Code, ecode.ErrNotFound.Message)
		return
	}

	response.Success(c, resp)
	return
}
