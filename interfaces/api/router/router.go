package router

import (
	"brandreviews/interfaces/api/handler/category"
	"brandreviews/interfaces/api/handler/tag"
	"brandreviews/interfaces/api/middleware"
	"github.com/gin-gonic/gin"
)

// ApiServer HTTP服务器
type ApiServer struct {
	tagHandler      *tag.TagHandler
	categoryHandler *category.CategoryHandler
}

// NewApiServer 创建API服务器
func NewApiServer(
	tagHandler *tag.TagHandler,
	categoryHandler *category.CategoryHandler,
) *ApiServer {
	return &ApiServer{
		tagHandler:      tagHandler,
		categoryHandler: categoryHandler,
	}
}

// setupPublicRoutes 设置公开路由
func (s *ApiServer) setupPublicRoutes(router *gin.Engine) {
	// 公共中间件
	router.Use(middleware.CORS())
	// api v1 路径
	v1 := router.Group("/api/v1")

	// tag相关（公开访问）
	v1.GET("/tag", s.tagHandler.GetTagList)

	v1.GET("/category", s.categoryHandler.GetCategoryList)
}

// Setup 设置路由
func (s *ApiServer) Setup(router *gin.Engine) {
	// 设置各类路由
	s.setupPublicRoutes(router)
}
