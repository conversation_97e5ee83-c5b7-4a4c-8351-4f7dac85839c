package router

import (
	"brandreviews/interfaces/api/handler/brand"
	"brandreviews/interfaces/api/handler/category"
	"brandreviews/interfaces/api/handler/coupon"
	"brandreviews/interfaces/api/handler/deal"
	"brandreviews/interfaces/api/handler/tag"
	"brandreviews/interfaces/api/middleware"

	"github.com/gin-gonic/gin"
)

// ApiServer HTTP服务器
type ApiServer struct {
	tagHandler      *tag.TagHandler
	categoryHandler *category.CategoryHandler
	brandHandler    *brand.BrandHandler
	dealHandler     *deal.DealHandler
	couponHandler   *coupon.CouponHandler
}

// NewApiServer 创建API服务器
func NewApiServer(
	tagHandler *tag.TagHandler,
	categoryHandler *category.CategoryHandler,
	brandHandler *brand.BrandHandler,
	dealHandler *deal.DealHandler,
	couponHandler *coupon.CouponHandler,
) *ApiServer {
	return &ApiServer{
		tagHandler:      tagHandler,
		categoryHandler: categoryHand<PERSON>,
		brandHandler:    brandHandler,
		dealHandler:     dealHandler,
		couponHandler:   couponHandler,
	}
}

// setupPublicRoutes 设置公开路由
func (s *ApiServer) setupPublicRoutes(router *gin.Engine) {
	// 公共中间件
	router.Use(middleware.CORS())
	// api v1 路径
	v1 := router.Group("/api/v1")

	// tag相关（公开访问）
	v1.GET("/tag", s.tagHandler.GetTagList)

	// category相关（公开访问）
	v1.GET("/category", s.categoryHandler.GetCategoryList)

	// brand相关（公开访问）
	v1.GET("/brand", s.brandHandler.GetBrandList)
	v1.GET("/brand/:id", s.brandHandler.GetBrandById)
	v1.GET("/brand/slug/:slug", s.brandHandler.GetBrandBySlug)

	// deal相关（公开访问）
	v1.GET("/deal", s.dealHandler.GetDealList)
	v1.GET("/deal/:id", s.dealHandler.GetDealById)
	v1.GET("/deal/slug/:slug", s.dealHandler.GetDealBySlug)

	// coupon相关（公开访问）
	v1.GET("/coupon", s.couponHandler.GetCouponList)
	v1.GET("/coupon/:id", s.couponHandler.GetCouponById)
	v1.GET("/coupon/slug/:slug", s.couponHandler.GetCouponBySlug)
	v1.GET("/coupon/code/:code", s.couponHandler.GetCouponByCode)
}

// Setup 设置路由
func (s *ApiServer) Setup(router *gin.Engine) {
	// 设置各类路由
	s.setupPublicRoutes(router)
}
