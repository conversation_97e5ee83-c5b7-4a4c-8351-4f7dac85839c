package middleware

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RequestTracer handles request tracing and metrics
type RequestTracer struct {
	logger  *zap.Logger
	metrics *RequestMetrics
}

// RequestMetrics holds request metrics
type RequestMetrics struct {
	mu                sync.RWMutex
	TotalRequests     int64
	TotalErrors       int64
	TotalDuration     time.Duration
	RequestsByMethod  map[string]int64
	RequestsByPath    map[string]int64
	RequestsByStatus  map[int]int64
	ResponseTimes     map[string][]time.Duration // path -> response times
	ActiveRequests    int64
	MaxActiveRequests int64
}

// NewRequestTracer creates a new request tracer
func NewRequestTracer(logger *zap.Logger) *RequestTracer {
	return &RequestTracer{
		logger: logger,
		metrics: &RequestMetrics{
			RequestsByMethod: make(map[string]int64),
			RequestsByPath:   make(map[string]int64),
			RequestsByStatus: make(map[int]int64),
			ResponseTimes:    make(map[string][]time.Duration),
		},
	}
}

// TraceID generates a unique trace ID for the request
func TraceID() string {
	return fmt.Sprintf("%d-%d", time.Now().UnixNano(), time.Now().Nanosecond())
}

// RequestTracing returns a gin middleware for request tracing
func (rt *RequestTracer) RequestTracing() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		traceID := TraceID()
		
		// Add trace ID to context and headers
		c.Set("trace_id", traceID)
		c.Header("X-Trace-ID", traceID)
		
		// Add trace ID to request context
		ctx := context.WithValue(c.Request.Context(), "trace_id", traceID)
		c.Request = c.Request.WithContext(ctx)
		
		// Increment active requests
		rt.metrics.mu.Lock()
		rt.metrics.ActiveRequests++
		if rt.metrics.ActiveRequests > rt.metrics.MaxActiveRequests {
			rt.metrics.MaxActiveRequests = rt.metrics.ActiveRequests
		}
		rt.metrics.mu.Unlock()
		
		// Log request start
		rt.logger.Info("Request started",
			zap.String("trace_id", traceID),
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.String("query", c.Request.URL.RawQuery),
			zap.String("user_agent", c.Request.UserAgent()),
			zap.String("client_ip", c.ClientIP()),
			zap.String("referer", c.Request.Referer()),
		)
		
		// Process request
		c.Next()
		
		// Calculate duration
		duration := time.Since(start)
		status := c.Writer.Status()
		
		// Update metrics
		rt.updateMetrics(c.Request.Method, c.FullPath(), status, duration)
		
		// Decrement active requests
		rt.metrics.mu.Lock()
		rt.metrics.ActiveRequests--
		rt.metrics.mu.Unlock()
		
		// Log request completion
		logLevel := zap.InfoLevel
		if status >= 400 {
			logLevel = zap.WarnLevel
		}
		if status >= 500 {
			logLevel = zap.ErrorLevel
		}
		
		rt.logger.Log(logLevel, "Request completed",
			zap.String("trace_id", traceID),
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.Int("status", status),
			zap.Duration("duration", duration),
			zap.Int("response_size", c.Writer.Size()),
			zap.String("client_ip", c.ClientIP()),
		)
		
		// Log slow requests
		if duration > 5*time.Second {
			rt.logger.Warn("Slow request detected",
				zap.String("trace_id", traceID),
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.Duration("duration", duration),
			)
		}
	}
}

// updateMetrics updates request metrics
func (rt *RequestTracer) updateMetrics(method, path string, status int, duration time.Duration) {
	rt.metrics.mu.Lock()
	defer rt.metrics.mu.Unlock()
	
	rt.metrics.TotalRequests++
	rt.metrics.TotalDuration += duration
	
	if status >= 400 {
		rt.metrics.TotalErrors++
	}
	
	rt.metrics.RequestsByMethod[method]++
	rt.metrics.RequestsByPath[path]++
	rt.metrics.RequestsByStatus[status]++
	
	// Store response times (keep only last 100 for each path)
	if _, exists := rt.metrics.ResponseTimes[path]; !exists {
		rt.metrics.ResponseTimes[path] = make([]time.Duration, 0, 100)
	}
	
	responseTimes := rt.metrics.ResponseTimes[path]
	if len(responseTimes) >= 100 {
		// Remove oldest entry
		responseTimes = responseTimes[1:]
	}
	responseTimes = append(responseTimes, duration)
	rt.metrics.ResponseTimes[path] = responseTimes
}

// GetMetrics returns current metrics
func (rt *RequestTracer) GetMetrics() map[string]interface{} {
	rt.metrics.mu.RLock()
	defer rt.metrics.mu.RUnlock()
	
	// Calculate average response time
	avgResponseTime := time.Duration(0)
	if rt.metrics.TotalRequests > 0 {
		avgResponseTime = rt.metrics.TotalDuration / time.Duration(rt.metrics.TotalRequests)
	}
	
	// Calculate error rate
	errorRate := float64(0)
	if rt.metrics.TotalRequests > 0 {
		errorRate = float64(rt.metrics.TotalErrors) / float64(rt.metrics.TotalRequests) * 100
	}
	
	// Calculate percentiles for each path
	pathMetrics := make(map[string]interface{})
	for path, times := range rt.metrics.ResponseTimes {
		if len(times) > 0 {
			pathMetrics[path] = map[string]interface{}{
				"count":       len(times),
				"avg":         calculateAverage(times),
				"p50":         calculatePercentile(times, 50),
				"p95":         calculatePercentile(times, 95),
				"p99":         calculatePercentile(times, 99),
				"min":         calculateMin(times),
				"max":         calculateMax(times),
			}
		}
	}
	
	return map[string]interface{}{
		"total_requests":      rt.metrics.TotalRequests,
		"total_errors":        rt.metrics.TotalErrors,
		"error_rate":          errorRate,
		"avg_response_time":   avgResponseTime.String(),
		"active_requests":     rt.metrics.ActiveRequests,
		"max_active_requests": rt.metrics.MaxActiveRequests,
		"requests_by_method":  copyMap(rt.metrics.RequestsByMethod),
		"requests_by_path":    copyMap(rt.metrics.RequestsByPath),
		"requests_by_status":  copyIntMap(rt.metrics.RequestsByStatus),
		"path_metrics":        pathMetrics,
	}
}

// Helper functions for metrics calculation
func calculateAverage(times []time.Duration) time.Duration {
	if len(times) == 0 {
		return 0
	}
	
	total := time.Duration(0)
	for _, t := range times {
		total += t
	}
	return total / time.Duration(len(times))
}

func calculatePercentile(times []time.Duration, percentile int) time.Duration {
	if len(times) == 0 {
		return 0
	}
	
	// Simple percentile calculation (not sorting for performance)
	// In production, consider using a more efficient algorithm
	sorted := make([]time.Duration, len(times))
	copy(sorted, times)
	
	// Simple bubble sort for small arrays
	for i := 0; i < len(sorted); i++ {
		for j := 0; j < len(sorted)-1-i; j++ {
			if sorted[j] > sorted[j+1] {
				sorted[j], sorted[j+1] = sorted[j+1], sorted[j]
			}
		}
	}
	
	index := (len(sorted) * percentile) / 100
	if index >= len(sorted) {
		index = len(sorted) - 1
	}
	
	return sorted[index]
}

func calculateMin(times []time.Duration) time.Duration {
	if len(times) == 0 {
		return 0
	}
	
	min := times[0]
	for _, t := range times[1:] {
		if t < min {
			min = t
		}
	}
	return min
}

func calculateMax(times []time.Duration) time.Duration {
	if len(times) == 0 {
		return 0
	}
	
	max := times[0]
	for _, t := range times[1:] {
		if t > max {
			max = t
		}
	}
	return max
}

func copyMap(m map[string]int64) map[string]int64 {
	result := make(map[string]int64)
	for k, v := range m {
		result[k] = v
	}
	return result
}

func copyIntMap(m map[int]int64) map[string]int64 {
	result := make(map[string]int64)
	for k, v := range m {
		result[strconv.Itoa(k)] = v
	}
	return result
}

// ResetMetrics resets all metrics
func (rt *RequestTracer) ResetMetrics() {
	rt.metrics.mu.Lock()
	defer rt.metrics.mu.Unlock()
	
	rt.metrics.TotalRequests = 0
	rt.metrics.TotalErrors = 0
	rt.metrics.TotalDuration = 0
	rt.metrics.ActiveRequests = 0
	rt.metrics.MaxActiveRequests = 0
	
	rt.metrics.RequestsByMethod = make(map[string]int64)
	rt.metrics.RequestsByPath = make(map[string]int64)
	rt.metrics.RequestsByStatus = make(map[int]int64)
	rt.metrics.ResponseTimes = make(map[string][]time.Duration)
}

// GetTraceIDFromContext extracts trace ID from context
func GetTraceIDFromContext(ctx context.Context) string {
	if traceID, ok := ctx.Value("trace_id").(string); ok {
		return traceID
	}
	return ""
}

// GetTraceIDFromGin extracts trace ID from gin context
func GetTraceIDFromGin(c *gin.Context) string {
	if traceID, exists := c.Get("trace_id"); exists {
		if id, ok := traceID.(string); ok {
			return id
		}
	}
	return ""
}
