package middleware

import (
	"sync"
	"time"

	"brandreviews/infra/ecode"
	"brandreviews/infra/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CircuitState represents the state of a circuit breaker
type CircuitState int

const (
	StateClosed CircuitState = iota
	StateOpen
	StateHalfOpen
)

// String returns string representation of circuit state
func (s CircuitState) String() string {
	switch s {
	case StateClosed:
		return "CLOSED"
	case StateOpen:
		return "OPEN"
	case StateHalfOpen:
		return "HALF_OPEN"
	default:
		return "UNKNOWN"
	}
}

// CircuitBreakerConfig holds circuit breaker configuration
type CircuitBreakerConfig struct {
	MaxRequests      uint32         // Maximum requests allowed in half-open state
	Interval         time.Duration  // Time window for failure counting
	Timeout          time.Duration  // Time to wait before transitioning from open to half-open
	FailureThreshold uint32         // Number of failures to trigger open state
	SuccessThreshold uint32         // Number of successes to close circuit in half-open state
	IsFailure        func(int) bool // Function to determine if response is a failure
}

// DefaultCircuitBreakerConfig returns default circuit breaker configuration
func DefaultCircuitBreakerConfig() *CircuitBreakerConfig {
	return &CircuitBreakerConfig{
		MaxRequests:      10,
		Interval:         60 * time.Second,
		Timeout:          60 * time.Second,
		FailureThreshold: 5,
		SuccessThreshold: 3,
		IsFailure: func(statusCode int) bool {
			return statusCode >= 500 // Only 5xx errors are considered failures
		},
	}
}

// CircuitBreaker implements the circuit breaker pattern
type CircuitBreaker struct {
	mu              sync.RWMutex
	config          *CircuitBreakerConfig
	state           CircuitState
	failures        uint32
	successes       uint32
	requests        uint32
	lastFailureTime time.Time
	lastSuccessTime time.Time
	nextAttemptTime time.Time
	logger          *zap.Logger
}

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(config *CircuitBreakerConfig, logger *zap.Logger) *CircuitBreaker {
	if config == nil {
		config = DefaultCircuitBreakerConfig()
	}

	return &CircuitBreaker{
		config: config,
		state:  StateClosed,
		logger: logger,
	}
}

// CanExecute checks if the circuit breaker allows execution
func (cb *CircuitBreaker) CanExecute() bool {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	now := time.Now()

	switch cb.state {
	case StateClosed:
		return true
	case StateOpen:
		if now.After(cb.nextAttemptTime) {
			cb.state = StateHalfOpen
			cb.requests = 0
			cb.successes = 0
			cb.logger.Info("Circuit breaker transitioning to half-open state")
			return true
		}
		return false
	case StateHalfOpen:
		return cb.requests < cb.config.MaxRequests
	default:
		return false
	}
}

// RecordSuccess records a successful execution
func (cb *CircuitBreaker) RecordSuccess() {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.lastSuccessTime = time.Now()

	switch cb.state {
	case StateClosed:
		cb.resetFailures()
	case StateHalfOpen:
		cb.successes++
		cb.requests++
		if cb.successes >= cb.config.SuccessThreshold {
			cb.state = StateClosed
			cb.resetCounters()
			cb.logger.Info("Circuit breaker closed after successful requests")
		}
	}
}

// RecordFailure records a failed execution
func (cb *CircuitBreaker) RecordFailure() {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.lastFailureTime = time.Now()

	switch cb.state {
	case StateClosed:
		cb.failures++
		if cb.failures >= cb.config.FailureThreshold {
			cb.state = StateOpen
			cb.nextAttemptTime = time.Now().Add(cb.config.Timeout)
			cb.logger.Warn("Circuit breaker opened due to failures",
				zap.Uint32("failures", cb.failures),
				zap.Time("next_attempt", cb.nextAttemptTime),
			)
		}
	case StateHalfOpen:
		cb.state = StateOpen
		cb.nextAttemptTime = time.Now().Add(cb.config.Timeout)
		cb.logger.Warn("Circuit breaker opened from half-open state due to failure")
	}
}

// resetFailures resets failure counter if interval has passed
func (cb *CircuitBreaker) resetFailures() {
	if time.Since(cb.lastFailureTime) > cb.config.Interval {
		cb.failures = 0
	}
}

// resetCounters resets all counters
func (cb *CircuitBreaker) resetCounters() {
	cb.failures = 0
	cb.successes = 0
	cb.requests = 0
}

// GetState returns current circuit breaker state
func (cb *CircuitBreaker) GetState() CircuitState {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.state
}

// GetStats returns circuit breaker statistics
func (cb *CircuitBreaker) GetStats() map[string]interface{} {
	cb.mu.RLock()
	defer cb.mu.RUnlock()

	return map[string]interface{}{
		"state":             cb.state.String(),
		"failures":          cb.failures,
		"successes":         cb.successes,
		"requests":          cb.requests,
		"last_failure_time": cb.lastFailureTime,
		"last_success_time": cb.lastSuccessTime,
		"next_attempt_time": cb.nextAttemptTime,
		"failure_threshold": cb.config.FailureThreshold,
		"success_threshold": cb.config.SuccessThreshold,
		"max_requests":      cb.config.MaxRequests,
	}
}

// CircuitBreakerManager manages multiple circuit breakers for different endpoints
type CircuitBreakerManager struct {
	breakers sync.Map
	config   *CircuitBreakerConfig
	logger   *zap.Logger
}

// NewCircuitBreakerManager creates a new circuit breaker manager
func NewCircuitBreakerManager(config *CircuitBreakerConfig, logger *zap.Logger) *CircuitBreakerManager {
	if config == nil {
		config = DefaultCircuitBreakerConfig()
	}

	return &CircuitBreakerManager{
		config: config,
		logger: logger,
	}
}

// getBreaker gets or creates a circuit breaker for the given key
func (cbm *CircuitBreakerManager) getBreaker(key string) *CircuitBreaker {
	if breaker, exists := cbm.breakers.Load(key); exists {
		return breaker.(*CircuitBreaker)
	}

	breaker := NewCircuitBreaker(cbm.config, cbm.logger)
	actual, _ := cbm.breakers.LoadOrStore(key, breaker)
	return actual.(*CircuitBreaker)
}

// CircuitBreakerMiddleware returns a gin middleware for circuit breaker
func (cbm *CircuitBreakerManager) CircuitBreakerMiddleware(keyFunc func(*gin.Context) string) gin.HandlerFunc {
	if keyFunc == nil {
		keyFunc = func(c *gin.Context) string {
			return c.FullPath()
		}
	}

	return func(c *gin.Context) {
		key := keyFunc(c)
		breaker := cbm.getBreaker(key)

		if !breaker.CanExecute() {
			cbm.logger.Warn("Circuit breaker is open, rejecting request",
				zap.String("key", key),
				zap.String("path", c.Request.URL.Path),
				zap.String("state", breaker.GetState().String()),
			)

			response.Error(c, ecode.ErrServiceUnavailable.Code, "Service temporarily unavailable")
			c.Abort()
			return
		}

		c.Next()

		// Record result based on response status
		status := c.Writer.Status()
		if cbm.config.IsFailure(status) {
			breaker.RecordFailure()
		} else {
			breaker.RecordSuccess()
		}
	}
}

// GetBreakerStats returns statistics for all circuit breakers
func (cbm *CircuitBreakerManager) GetBreakerStats() map[string]interface{} {
	stats := make(map[string]interface{})

	cbm.breakers.Range(func(key, value interface{}) bool {
		keyStr := key.(string)
		breaker := value.(*CircuitBreaker)
		stats[keyStr] = breaker.GetStats()
		return true
	})

	return stats
}

// ResetBreaker resets a specific circuit breaker
func (cbm *CircuitBreakerManager) ResetBreaker(key string) {
	if breaker, exists := cbm.breakers.Load(key); exists {
		cb := breaker.(*CircuitBreaker)
		cb.mu.Lock()
		cb.state = StateClosed
		cb.resetCounters()
		cb.mu.Unlock()

		cbm.logger.Info("Circuit breaker reset", zap.String("key", key))
	}
}

// ResetAllBreakers resets all circuit breakers
func (cbm *CircuitBreakerManager) ResetAllBreakers() {
	cbm.breakers.Range(func(key, value interface{}) bool {
		breaker := value.(*CircuitBreaker)

		breaker.mu.Lock()
		breaker.state = StateClosed
		breaker.resetCounters()
		breaker.mu.Unlock()

		return true
	})

	cbm.logger.Info("All circuit breakers reset")
}

// PathBasedCircuitBreaker creates a circuit breaker middleware based on request path
func PathBasedCircuitBreaker(config *CircuitBreakerConfig, logger *zap.Logger) gin.HandlerFunc {
	manager := NewCircuitBreakerManager(config, logger)
	return manager.CircuitBreakerMiddleware(func(c *gin.Context) string {
		return c.FullPath()
	})
}

// ServiceBasedCircuitBreaker creates a circuit breaker middleware for external service calls
func ServiceBasedCircuitBreaker(serviceName string, config *CircuitBreakerConfig, logger *zap.Logger) gin.HandlerFunc {
	manager := NewCircuitBreakerManager(config, logger)
	return manager.CircuitBreakerMiddleware(func(c *gin.Context) string {
		return serviceName
	})
}
