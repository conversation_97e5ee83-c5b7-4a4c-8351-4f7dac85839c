package middleware

import (
	"fmt"
	"html"
	"net/http"
	"reflect"
	"regexp"
	"strconv"
	"strings"

	"brandreviews/infra/ecode"
	"brandreviews/infra/response"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
)

// Validator holds the validator instance and custom validation functions
type Validator struct {
	validator *validator.Validate
	logger    *zap.Logger
}

// NewValidator creates a new validator instance
func NewValidator(logger *zap.Logger) *Validator {
	v := validator.New()
	
	// Register custom validation functions
	v.RegisterValidation("slug", validateSlug)
	v.RegisterValidation("alphanum", validateAlphaNum)
	v.RegisterValidation("phone", validatePhone)
	v.RegisterValidation("password", validatePassword)
	
	return &Validator{
		validator: v,
		logger:    logger,
	}
}

// validateSlug validates slug format (lowercase letters, numbers, hyphens)
func validateSlug(fl validator.FieldLevel) bool {
	slug := fl.Field().String()
	if slug == "" {
		return true // Let required validation handle empty values
	}
	
	// Slug should contain only lowercase letters, numbers, and hyphens
	// Should not start or end with hyphen
	matched, _ := regexp.MatchString(`^[a-z0-9]+(?:-[a-z0-9]+)*$`, slug)
	return matched
}

// validateAlphaNum validates alphanumeric characters only
func validateAlphaNum(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	if value == "" {
		return true
	}
	
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9]+$`, value)
	return matched
}

// validatePhone validates phone number format
func validatePhone(fl validator.FieldLevel) bool {
	phone := fl.Field().String()
	if phone == "" {
		return true
	}
	
	// Simple phone validation - can be enhanced based on requirements
	matched, _ := regexp.MatchString(`^\+?[1-9]\d{1,14}$`, phone)
	return matched
}

// validatePassword validates password strength
func validatePassword(fl validator.FieldLevel) bool {
	password := fl.Field().String()
	if password == "" {
		return true
	}
	
	// Password should be at least 8 characters and contain at least one letter and one number
	if len(password) < 8 {
		return false
	}
	
	hasLetter, _ := regexp.MatchString(`[a-zA-Z]`, password)
	hasNumber, _ := regexp.MatchString(`[0-9]`, password)
	
	return hasLetter && hasNumber
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Tag     string `json:"tag"`
	Value   string `json:"value"`
	Message string `json:"message"`
}

// ValidateStruct validates a struct and returns formatted errors
func (v *Validator) ValidateStruct(s interface{}) []ValidationError {
	var errors []ValidationError
	
	err := v.validator.Struct(s)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			errors = append(errors, ValidationError{
				Field:   err.Field(),
				Tag:     err.Tag(),
				Value:   fmt.Sprintf("%v", err.Value()),
				Message: v.getErrorMessage(err),
			})
		}
	}
	
	return errors
}

// getErrorMessage returns a user-friendly error message for validation errors
func (v *Validator) getErrorMessage(err validator.FieldError) string {
	field := err.Field()
	tag := err.Tag()
	param := err.Param()
	
	switch tag {
	case "required":
		return fmt.Sprintf("%s is required", field)
	case "min":
		return fmt.Sprintf("%s must be at least %s characters long", field, param)
	case "max":
		return fmt.Sprintf("%s must be at most %s characters long", field, param)
	case "email":
		return fmt.Sprintf("%s must be a valid email address", field)
	case "url":
		return fmt.Sprintf("%s must be a valid URL", field)
	case "slug":
		return fmt.Sprintf("%s must be a valid slug (lowercase letters, numbers, hyphens)", field)
	case "alphanum":
		return fmt.Sprintf("%s must contain only alphanumeric characters", field)
	case "phone":
		return fmt.Sprintf("%s must be a valid phone number", field)
	case "password":
		return fmt.Sprintf("%s must be at least 8 characters with letters and numbers", field)
	case "oneof":
		return fmt.Sprintf("%s must be one of: %s", field, param)
	case "gte":
		return fmt.Sprintf("%s must be greater than or equal to %s", field, param)
	case "lte":
		return fmt.Sprintf("%s must be less than or equal to %s", field, param)
	case "gt":
		return fmt.Sprintf("%s must be greater than %s", field, param)
	case "lt":
		return fmt.Sprintf("%s must be less than %s", field, param)
	default:
		return fmt.Sprintf("%s is invalid", field)
	}
}

// ValidateAndSanitize middleware validates and sanitizes request data
func (v *Validator) ValidateAndSanitize() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Sanitize query parameters
		v.sanitizeQuery(c)
		
		// Sanitize path parameters
		v.sanitizePath(c)
		
		c.Next()
	}
}

// sanitizeQuery sanitizes query parameters
func (v *Validator) sanitizeQuery(c *gin.Context) {
	query := c.Request.URL.Query()
	
	for key, values := range query {
		for i, value := range values {
			// HTML escape to prevent XSS
			sanitized := html.EscapeString(value)
			
			// Remove potentially dangerous characters
			sanitized = strings.ReplaceAll(sanitized, "<script", "")
			sanitized = strings.ReplaceAll(sanitized, "</script>", "")
			sanitized = strings.ReplaceAll(sanitized, "javascript:", "")
			sanitized = strings.ReplaceAll(sanitized, "vbscript:", "")
			sanitized = strings.ReplaceAll(sanitized, "onload=", "")
			sanitized = strings.ReplaceAll(sanitized, "onerror=", "")
			
			values[i] = sanitized
		}
		query[key] = values
	}
	
	c.Request.URL.RawQuery = query.Encode()
}

// sanitizePath sanitizes path parameters
func (v *Validator) sanitizePath(c *gin.Context) {
	for _, param := range c.Params {
		// HTML escape path parameters
		sanitized := html.EscapeString(param.Value)
		
		// Update the parameter value
		for i, p := range c.Params {
			if p.Key == param.Key {
				c.Params[i].Value = sanitized
				break
			}
		}
	}
}

// ValidateJSON validates JSON request body
func (v *Validator) ValidateJSON(obj interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method == http.MethodGet || c.Request.Method == http.MethodDelete {
			c.Next()
			return
		}
		
		// Create a new instance of the object type
		objType := reflect.TypeOf(obj)
		if objType.Kind() == reflect.Ptr {
			objType = objType.Elem()
		}
		newObj := reflect.New(objType).Interface()
		
		// Bind JSON to the object
		if err := c.ShouldBindJSON(newObj); err != nil {
			v.logger.Warn("JSON binding failed",
				zap.Error(err),
				zap.String("path", c.Request.URL.Path),
			)
			
			response.Error(c, ecode.ErrInvalidParameter.Code, "Invalid JSON format")
			c.Abort()
			return
		}
		
		// Validate the object
		if validationErrors := v.ValidateStruct(newObj); len(validationErrors) > 0 {
			v.logger.Warn("Validation failed",
				zap.Any("errors", validationErrors),
				zap.String("path", c.Request.URL.Path),
			)
			
			response.ErrorWithData(c, ecode.ErrValidation.Code, "Validation failed", map[string]interface{}{
				"errors": validationErrors,
			})
			c.Abort()
			return
		}
		
		// Store validated object in context
		c.Set("validated_data", newObj)
		c.Next()
	}
}

// ValidateQuery validates query parameters
func (v *Validator) ValidateQuery(obj interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create a new instance of the object type
		objType := reflect.TypeOf(obj)
		if objType.Kind() == reflect.Ptr {
			objType = objType.Elem()
		}
		newObj := reflect.New(objType).Interface()
		
		// Bind query parameters to the object
		if err := c.ShouldBindQuery(newObj); err != nil {
			v.logger.Warn("Query binding failed",
				zap.Error(err),
				zap.String("path", c.Request.URL.Path),
			)
			
			response.Error(c, ecode.ErrInvalidParameter.Code, "Invalid query parameters")
			c.Abort()
			return
		}
		
		// Validate the object
		if validationErrors := v.ValidateStruct(newObj); len(validationErrors) > 0 {
			v.logger.Warn("Query validation failed",
				zap.Any("errors", validationErrors),
				zap.String("path", c.Request.URL.Path),
			)
			
			response.ErrorWithData(c, ecode.ErrValidation.Code, "Query validation failed", map[string]interface{}{
				"errors": validationErrors,
			})
			c.Abort()
			return
		}
		
		// Store validated object in context
		c.Set("validated_query", newObj)
		c.Next()
	}
}

// ValidatePathParam validates path parameters
func ValidatePathParam(paramName string, validationFunc func(string) bool, errorMessage string) gin.HandlerFunc {
	return func(c *gin.Context) {
		param := c.Param(paramName)
		
		if param == "" {
			response.Error(c, ecode.ErrInvalidParameter.Code, fmt.Sprintf("%s is required", paramName))
			c.Abort()
			return
		}
		
		if !validationFunc(param) {
			response.Error(c, ecode.ErrInvalidParameter.Code, errorMessage)
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// ValidateID validates ID path parameter
func ValidateID(paramName string) gin.HandlerFunc {
	return ValidatePathParam(paramName, func(value string) bool {
		id, err := strconv.ParseUint(value, 10, 64)
		return err == nil && id > 0
	}, fmt.Sprintf("%s must be a valid positive integer", paramName))
}

// ValidateSlug validates slug path parameter
func ValidateSlug(paramName string) gin.HandlerFunc {
	return ValidatePathParam(paramName, func(value string) bool {
		matched, _ := regexp.MatchString(`^[a-z0-9]+(?:-[a-z0-9]+)*$`, value)
		return matched
	}, fmt.Sprintf("%s must be a valid slug", paramName))
}

// GetValidatedData retrieves validated data from context
func GetValidatedData(c *gin.Context) (interface{}, bool) {
	return c.Get("validated_data")
}

// GetValidatedQuery retrieves validated query from context
func GetValidatedQuery(c *gin.Context) (interface{}, bool) {
	return c.Get("validated_query")
}
