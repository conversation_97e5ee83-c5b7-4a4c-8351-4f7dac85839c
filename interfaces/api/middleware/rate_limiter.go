package middleware

import (
	"fmt"
	"sync"
	"time"

	"brandreviews/infra/ecode"
	"brandreviews/infra/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// TokenBucket represents a token bucket for rate limiting
type TokenBucket struct {
	capacity   int64
	tokens     int64
	refillRate int64 // tokens per second
	lastRefill time.Time
	mu         sync.Mutex
}

// NewTokenBucket creates a new token bucket
func NewTokenBucket(capacity, refillRate int64) *TokenBucket {
	return &TokenBucket{
		capacity:   capacity,
		tokens:     capacity,
		refillRate: refillRate,
		lastRefill: time.Now(),
	}
}

// TakeToken attempts to take a token from the bucket
func (tb *TokenBucket) TakeToken() bool {
	tb.mu.Lock()
	defer tb.mu.Unlock()

	now := time.Now()
	elapsed := now.Sub(tb.lastRefill).Seconds()

	// Refill tokens based on elapsed time
	tokensToAdd := int64(elapsed * float64(tb.refillRate))
	if tokensToAdd > 0 {
		tb.tokens = min(tb.capacity, tb.tokens+tokensToAdd)
		tb.lastRefill = now
	}

	if tb.tokens > 0 {
		tb.tokens--
		return true
	}

	return false
}

// GetTokens returns current token count
func (tb *TokenBucket) GetTokens() int64 {
	tb.mu.Lock()
	defer tb.mu.Unlock()
	return tb.tokens
}

// RateLimiter manages rate limiting for different clients
type RateLimiter struct {
	buckets    sync.Map
	capacity   int64
	refillRate int64
	logger     *zap.Logger
}

// RateLimiterConfig holds rate limiter configuration
type RateLimiterConfig struct {
	RequestsPerSecond int64
	BurstSize         int64
	KeyFunc           func(*gin.Context) string
}

// DefaultRateLimiterConfig returns default rate limiter configuration
func DefaultRateLimiterConfig() *RateLimiterConfig {
	return &RateLimiterConfig{
		RequestsPerSecond: 100, // 100 requests per second
		BurstSize:         200, // Allow burst of 200 requests
		KeyFunc: func(c *gin.Context) string {
			// Use client IP as default key
			return c.ClientIP()
		},
	}
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(config *RateLimiterConfig, logger *zap.Logger) *RateLimiter {
	if config == nil {
		config = DefaultRateLimiterConfig()
	}

	return &RateLimiter{
		buckets:    sync.Map{},
		capacity:   config.BurstSize,
		refillRate: config.RequestsPerSecond,
		logger:     logger,
	}
}

// getBucket gets or creates a token bucket for the given key
func (rl *RateLimiter) getBucket(key string) *TokenBucket {
	if bucket, exists := rl.buckets.Load(key); exists {
		return bucket.(*TokenBucket)
	}

	bucket := NewTokenBucket(rl.capacity, rl.refillRate)
	actual, _ := rl.buckets.LoadOrStore(key, bucket)
	return actual.(*TokenBucket)
}

// RateLimit returns a gin middleware for rate limiting
func (rl *RateLimiter) RateLimit(config *RateLimiterConfig) gin.HandlerFunc {
	if config == nil {
		config = DefaultRateLimiterConfig()
	}

	return func(c *gin.Context) {
		key := config.KeyFunc(c)
		bucket := rl.getBucket(key)

		if !bucket.TakeToken() {
			rl.logger.Warn("Rate limit exceeded",
				zap.String("client", key),
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method),
			)

			// Add rate limit headers
			c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", rl.capacity))
			c.Header("X-RateLimit-Remaining", "0")
			c.Header("X-RateLimit-Reset", fmt.Sprintf("%d", time.Now().Add(time.Second).Unix()))

			response.Error(c, ecode.ErrTooManyRequests.Code, "Rate limit exceeded")
			c.Abort()
			return
		}

		// Add rate limit headers
		remaining := bucket.GetTokens()
		c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", rl.capacity))
		c.Header("X-RateLimit-Remaining", fmt.Sprintf("%d", remaining))
		c.Header("X-RateLimit-Reset", fmt.Sprintf("%d", time.Now().Add(time.Second).Unix()))

		c.Next()
	}
}

// CleanupOldBuckets removes old unused buckets (should be called periodically)
func (rl *RateLimiter) CleanupOldBuckets() {
	cutoff := time.Now().Add(-1 * time.Hour) // Remove buckets older than 1 hour

	rl.buckets.Range(func(key, value interface{}) bool {
		bucket := value.(*TokenBucket)
		bucket.mu.Lock()
		lastUsed := bucket.lastRefill
		bucket.mu.Unlock()

		if lastUsed.Before(cutoff) {
			rl.buckets.Delete(key)
		}
		return true
	})
}

// GetStats returns rate limiter statistics
func (rl *RateLimiter) GetStats() map[string]interface{} {
	bucketCount := 0
	rl.buckets.Range(func(key, value interface{}) bool {
		bucketCount++
		return true
	})

	return map[string]interface{}{
		"bucket_count":        bucketCount,
		"capacity_per_bucket": rl.capacity,
		"refill_rate":         rl.refillRate,
	}
}

// Helper function for min
func min(a, b int64) int64 {
	if a < b {
		return a
	}
	return b
}

// IPBasedRateLimit creates a rate limiter based on client IP
func IPBasedRateLimit(requestsPerSecond, burstSize int64, logger *zap.Logger) gin.HandlerFunc {
	config := &RateLimiterConfig{
		RequestsPerSecond: requestsPerSecond,
		BurstSize:         burstSize,
		KeyFunc: func(c *gin.Context) string {
			return c.ClientIP()
		},
	}

	limiter := NewRateLimiter(config, logger)
	return limiter.RateLimit(config)
}

// UserBasedRateLimit creates a rate limiter based on user ID (requires authentication)
func UserBasedRateLimit(requestsPerSecond, burstSize int64, logger *zap.Logger) gin.HandlerFunc {
	config := &RateLimiterConfig{
		RequestsPerSecond: requestsPerSecond,
		BurstSize:         burstSize,
		KeyFunc: func(c *gin.Context) string {
			// Try to get user ID from context (set by auth middleware)
			if userID, exists := c.Get("user_id"); exists {
				return fmt.Sprintf("user:%v", userID)
			}
			// Fallback to IP if no user ID
			return fmt.Sprintf("ip:%s", c.ClientIP())
		},
	}

	limiter := NewRateLimiter(config, logger)
	return limiter.RateLimit(config)
}

// EndpointBasedRateLimit creates a rate limiter based on endpoint and client IP
func EndpointBasedRateLimit(requestsPerSecond, burstSize int64, logger *zap.Logger) gin.HandlerFunc {
	config := &RateLimiterConfig{
		RequestsPerSecond: requestsPerSecond,
		BurstSize:         burstSize,
		KeyFunc: func(c *gin.Context) string {
			return fmt.Sprintf("%s:%s:%s", c.ClientIP(), c.Request.Method, c.FullPath())
		},
	}

	limiter := NewRateLimiter(config, logger)
	return limiter.RateLimit(config)
}
