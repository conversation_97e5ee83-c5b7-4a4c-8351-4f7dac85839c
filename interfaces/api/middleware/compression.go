package middleware

import (
	"compress/gzip"
	"io"
	"net/http"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
)

// CompressionLevel defines compression levels
type CompressionLevel int

const (
	DefaultCompression CompressionLevel = gzip.DefaultCompression
	NoCompression      CompressionLevel = gzip.NoCompression
	BestSpeed          CompressionLevel = gzip.BestSpeed
	BestCompression    CompressionLevel = gzip.BestCompression
)

// CompressionConfig holds compression configuration
type CompressionConfig struct {
	Level             CompressionLevel
	MinSize           int      // Minimum response size to compress (bytes)
	ExcludedPaths     []string // Paths to exclude from compression
	ExcludedMimeTypes []string // MIME types to exclude from compression
}

// DefaultCompressionConfig returns default compression configuration
func DefaultCompressionConfig() *CompressionConfig {
	return &CompressionConfig{
		Level:   DefaultCompression,
		MinSize: 1024, // 1KB minimum
		ExcludedPaths: []string{
			"/health",
			"/metrics",
		},
		ExcludedMimeTypes: []string{
			"image/jpeg",
			"image/png",
			"image/gif",
			"image/webp",
			"video/mp4",
			"video/webm",
			"audio/mpeg",
			"audio/wav",
			"application/zip",
			"application/gzip",
			"application/x-gzip",
		},
	}
}

// gzipWriter wraps gin.ResponseWriter with gzip compression
type gzipWriter struct {
	gin.ResponseWriter
	writer *gzip.Writer
}

// Write compresses and writes data
func (g *gzipWriter) Write(data []byte) (int, error) {
	return g.writer.Write(data)
}

// WriteString compresses and writes string data
func (g *gzipWriter) WriteString(s string) (int, error) {
	return g.writer.Write([]byte(s))
}

// Close closes the gzip writer
func (g *gzipWriter) Close() error {
	return g.writer.Close()
}

// Flush flushes the gzip writer
func (g *gzipWriter) Flush() {
	g.writer.Flush()
	if flusher, ok := g.ResponseWriter.(http.Flusher); ok {
		flusher.Flush()
	}
}

// gzipWriterPool manages a pool of gzip writers for reuse
var gzipWriterPool = sync.Pool{
	New: func() interface{} {
		writer, _ := gzip.NewWriterLevel(io.Discard, gzip.DefaultCompression)
		return writer
	},
}

// getGzipWriter gets a gzip writer from the pool
func getGzipWriter(w io.Writer, level CompressionLevel) *gzip.Writer {
	writer := gzipWriterPool.Get().(*gzip.Writer)
	writer.Reset(w)

	// Set compression level if different from default
	if level != DefaultCompression {
		writer.Close()
		writer, _ = gzip.NewWriterLevel(w, int(level))
	}

	return writer
}

// putGzipWriter returns a gzip writer to the pool
func putGzipWriter(writer *gzip.Writer) {
	writer.Reset(io.Discard)
	gzipWriterPool.Put(writer)
}

// shouldCompress determines if the response should be compressed
func shouldCompress(c *gin.Context, config *CompressionConfig) bool {
	// Check if client accepts gzip
	if !strings.Contains(c.GetHeader("Accept-Encoding"), "gzip") {
		return false
	}

	// Check excluded paths
	path := c.Request.URL.Path
	for _, excludedPath := range config.ExcludedPaths {
		if path == excludedPath || strings.HasPrefix(path, excludedPath) {
			return false
		}
	}

	// Check content type after response is written
	contentType := c.Writer.Header().Get("Content-Type")
	if contentType != "" {
		for _, excludedType := range config.ExcludedMimeTypes {
			if strings.Contains(contentType, excludedType) {
				return false
			}
		}
	}

	return true
}

// Gzip returns a gin middleware for gzip compression
func Gzip(config *CompressionConfig) gin.HandlerFunc {
	if config == nil {
		config = DefaultCompressionConfig()
	}

	return func(c *gin.Context) {
		if !shouldCompress(c, config) {
			c.Next()
			return
		}

		// Set compression headers
		c.Header("Content-Encoding", "gzip")
		c.Header("Vary", "Accept-Encoding")

		// Create gzip writer
		gzWriter := getGzipWriter(c.Writer, config.Level)
		defer func() {
			gzWriter.Close()
			putGzipWriter(gzWriter)
		}()

		// Wrap response writer
		c.Writer = &gzipWriter{
			ResponseWriter: c.Writer,
			writer:         gzWriter,
		}

		c.Next()
	}
}

// GzipWithLevel returns a gin middleware for gzip compression with specific level
func GzipWithLevel(level CompressionLevel) gin.HandlerFunc {
	config := DefaultCompressionConfig()
	config.Level = level
	return Gzip(config)
}

// conditionalGzipWriter wraps gin.ResponseWriter with conditional gzip compression
type conditionalGzipWriter struct {
	gin.ResponseWriter
	config     *CompressionConfig
	gzipWriter *gzip.Writer
	buffer     []byte
	compressed bool
	written    bool
}

// Write implements conditional compression based on response size and content type
func (w *conditionalGzipWriter) Write(data []byte) (int, error) {
	if !w.written {
		w.written = true

		// Check content type
		contentType := w.Header().Get("Content-Type")
		if contentType == "" {
			contentType = http.DetectContentType(data)
			w.Header().Set("Content-Type", contentType)
		}

		// Check if we should compress based on content type
		shouldCompress := true
		for _, excludedType := range w.config.ExcludedMimeTypes {
			if strings.Contains(contentType, excludedType) {
				shouldCompress = false
				break
			}
		}

		// Buffer data until we reach minimum size or end of response
		if shouldCompress && len(data) < w.config.MinSize {
			w.buffer = append(w.buffer, data...)
			return len(data), nil
		}

		// Decide whether to compress
		if shouldCompress && (len(w.buffer)+len(data)) >= w.config.MinSize {
			w.compressed = true
			w.Header().Set("Content-Encoding", "gzip")
			w.Header().Set("Vary", "Accept-Encoding")

			w.gzipWriter = getGzipWriter(w.ResponseWriter, w.config.Level)

			// Write buffered data
			if len(w.buffer) > 0 {
				w.gzipWriter.Write(w.buffer)
				w.buffer = nil
			}

			return w.gzipWriter.Write(data)
		}

		// Write without compression
		if len(w.buffer) > 0 {
			w.ResponseWriter.Write(w.buffer)
			w.buffer = nil
		}
		return w.ResponseWriter.Write(data)
	}

	// Subsequent writes
	if w.compressed {
		return w.gzipWriter.Write(data)
	}
	return w.ResponseWriter.Write(data)
}

// WriteString implements conditional compression for string data
func (w *conditionalGzipWriter) WriteString(s string) (int, error) {
	return w.Write([]byte(s))
}

// Close closes the gzip writer if compression was used
func (w *conditionalGzipWriter) Close() error {
	// Write any remaining buffered data
	if len(w.buffer) > 0 && !w.compressed {
		w.ResponseWriter.Write(w.buffer)
		w.buffer = nil
	}

	if w.gzipWriter != nil {
		err := w.gzipWriter.Close()
		putGzipWriter(w.gzipWriter)
		return err
	}
	return nil
}

// Flush flushes the writer
func (w *conditionalGzipWriter) Flush() {
	if w.gzipWriter != nil {
		w.gzipWriter.Flush()
	}
	if flusher, ok := w.ResponseWriter.(http.Flusher); ok {
		flusher.Flush()
	}
}

// ConditionalGzip returns a gin middleware for conditional gzip compression
// This middleware only compresses responses that meet size and content type criteria
func ConditionalGzip(config *CompressionConfig) gin.HandlerFunc {
	if config == nil {
		config = DefaultCompressionConfig()
	}

	return func(c *gin.Context) {
		if !strings.Contains(c.GetHeader("Accept-Encoding"), "gzip") {
			c.Next()
			return
		}

		// Check excluded paths
		path := c.Request.URL.Path
		for _, excludedPath := range config.ExcludedPaths {
			if path == excludedPath || strings.HasPrefix(path, excludedPath) {
				c.Next()
				return
			}
		}

		// Wrap response writer
		writer := &conditionalGzipWriter{
			ResponseWriter: c.Writer,
			config:         config,
		}

		c.Writer = writer

		defer func() {
			writer.Close()
		}()

		c.Next()
	}
}
