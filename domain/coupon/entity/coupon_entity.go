package entity

import (
	"time"
)

// Coupon represents a coupon entity with optimized field ordering for memory alignment
type Coupon struct {
	// 8-byte aligned fields first
	Id            uint64    `json:"id" gorm:"primaryKey;index:idx_coupon_id"`
	BrandId       uint64    `json:"brand_id" gorm:"index:idx_coupon_brand"`
	CategoryId    uint64    `json:"category_id" gorm:"index:idx_coupon_category"`
	StartDate     uint64    `json:"start_date" gorm:"index:idx_coupon_start_date"`
	EndDate       uint64    `json:"end_date" gorm:"index:idx_coupon_end_date"`
	DiscountValue float64   `json:"discount_value" gorm:"type:decimal(12,2)" validate:"omitempty,min=0"`
	MinOrderValue float64   `json:"min_order_value" gorm:"type:decimal(12,2)" validate:"omitempty,min=0"`
	MaxDiscount   float64   `json:"max_discount" gorm:"type:decimal(12,2)" validate:"omitempty,min=0"`
	CreatedAt     time.Time `json:"created_at" gorm:"index:idx_coupon_created"`
	UpdatedAt     time.Time `json:"updated_at"`

	// 4-byte aligned fields
	UsageLimit     int `json:"usage_limit" gorm:"type:int;default:0" validate:"min=0"` // 0 means unlimited
	UsedCount      int `json:"used_count" gorm:"type:int;default:0" validate:"min=0"`
	UserUsageLimit int `json:"user_usage_limit" gorm:"type:int;default:1" validate:"min=1"` // per user limit

	// String fields (pointer size - 8 bytes on 64-bit)
	Slug         string `json:"slug" gorm:"uniqueIndex:idx_coupon_slug;type:varchar(100);not null" validate:"required,min=3,max=100,slug"`
	Title        string `json:"title" gorm:"type:varchar(255);not null;index:idx_coupon_title" validate:"required,min=5,max=255"`
	Description  string `json:"description" gorm:"type:text" validate:"max=2000"`
	Code         string `json:"code" gorm:"uniqueIndex:idx_coupon_code;type:varchar(50);not null" validate:"required,min=3,max=50,alphanum"`
	CouponUrl    string `json:"coupon_url" gorm:"type:varchar(1000);not null" validate:"required,url,max=1000"`
	DiscountType string `json:"discount_type" gorm:"type:varchar(20);not null;index:idx_coupon_discount_type" validate:"required,oneof=percentage fixed free_shipping"`
	BrandSlug    string `json:"brand_slug" gorm:"type:varchar(100);index:idx_coupon_brand_slug" validate:"omitempty,min=3,max=100"`
	CategorySlug string `json:"category_slug" gorm:"type:varchar(100);index:idx_coupon_category_slug" validate:"omitempty,min=3,max=100"`

	// Boolean fields (1 byte each, grouped together for better packing)
	Featured bool `json:"featured" gorm:"type:boolean;default:false;index:idx_coupon_featured"`
	Active   bool `json:"active" gorm:"type:boolean;default:true;index:idx_coupon_active"`
	Verified bool `json:"verified" gorm:"type:boolean;default:false;index:idx_coupon_verified"`
}

// TableName returns the table name for Coupon entity
func (Coupon) TableName() string {
	return "coupons"
}

// IsValid checks if the coupon is currently valid based on time constraints and usage limits
func (c *Coupon) IsValid() bool {
	if !c.Active {
		return false
	}

	// Check time constraints
	now := uint64(time.Now().Unix())
	if (c.StartDate != 0 && c.StartDate > now) || (c.EndDate != 0 && c.EndDate < now) {
		return false
	}

	// Check usage limits
	if c.UsageLimit > 0 && c.UsedCount >= c.UsageLimit {
		return false
	}

	return true
}

// IsAvailable checks if the coupon is available for use
func (c *Coupon) IsAvailable() bool {
	return c.IsValid() && (c.UsageLimit == 0 || c.UsedCount < c.UsageLimit)
}
