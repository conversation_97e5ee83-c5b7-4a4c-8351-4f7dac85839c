package entity

import (
	"time"
)

type Coupon struct {
	Id          uint64    `json:"id" gorm:"primaryKey"`
	Slug        string    `json:"slug" gorm:"uniqueIndex;not null"`
	Title       string    `json:"title" gorm:"type:varchar(255);not null"`
	Description string    `json:"description" gorm:"type:text"`
	Code        string    `json:"code" gorm:"type:varchar(100);not null"`
	CouponUrl   string    `json:"coupon_url" gorm:"type:varchar(500);not null"`
	
	// Discount information
	DiscountType  string  `json:"discount_type" gorm:"type:varchar(50);not null"` // percentage, fixed, free_shipping
	DiscountValue float64 `json:"discount_value" gorm:"type:decimal(10,2)"`
	MinOrderValue float64 `json:"min_order_value" gorm:"type:decimal(10,2)"`
	MaxDiscount   float64 `json:"max_discount" gorm:"type:decimal(10,2)"`
	
	// Status and visibility
	Featured bool `json:"featured" gorm:"type:boolean;default:false"`
	Active   bool `json:"active" gorm:"type:boolean;default:true"`
	Verified bool `json:"verified" gorm:"type:boolean;default:false"`
	
	// Time constraints
	StartDate uint64 `json:"start_date"`
	EndDate   uint64 `json:"end_date"`
	
	// Usage constraints
	UsageLimit     int `json:"usage_limit" gorm:"type:int;default:0"`      // 0 means unlimited
	UsedCount      int `json:"used_count" gorm:"type:int;default:0"`
	UserUsageLimit int `json:"user_usage_limit" gorm:"type:int;default:1"` // per user limit
	
	// Brand association (no foreign key for performance)
	BrandId   uint64 `json:"brand_id"`
	BrandSlug string `json:"brand_slug" gorm:"type:varchar(255)"`
	
	// Category association (derived from brand)
	CategoryId   uint64 `json:"category_id"`
	CategorySlug string `json:"category_slug" gorm:"type:varchar(255)"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
