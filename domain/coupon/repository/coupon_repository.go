package repository

import (
	"brandreviews/domain/coupon/entity"
	"brandreviews/infra/ecode"
	"github.com/gin-gonic/gin"
)

type CouponRepository interface {
	CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error
	UpdateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error
	DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error
	GetCouponDetailById(ctx *gin.Context, id uint64) (*entity.Coupon, *ecode.Error)
	GetCouponListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Coupon, int64, *ecode.Error)
	GetCouponDetailBySlug(ctx *gin.Context, slug string) (*entity.Coupon, *ecode.Error)
	GetCouponDetailByCode(ctx *gin.Context, code string) (*entity.Coupon, *ecode.Error)
	GetCouponCount(ctx *gin.Context) (int64, *ecode.Error)
}
