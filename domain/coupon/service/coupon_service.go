package service

import (
	"brandreviews/domain/coupon/entity"
	"brandreviews/domain/coupon/repository"
	"brandreviews/infra/ecode"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CouponService interface {
	CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error
	UpdateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error
	DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error
	GetCouponDetailById(ctx *gin.Context, id uint64) (*entity.Coupon, *ecode.Error)
	GetCouponListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Coupon, int64, *ecode.Error)
	GetCouponDetailBySlug(ctx *gin.Context, slug string) (*entity.Coupon, *ecode.Error)
	GetCouponDetailByCode(ctx *gin.Context, code string) (*entity.Coupon, *ecode.Error)
	GetCouponCount(ctx *gin.Context) (int64, *ecode.Error)
}

type CouponServiceImpl struct {
	repo   repository.CouponRepository
	logger *zap.Logger
}

func NewCouponService(repo repository.CouponRepository, logger *zap.Logger) CouponService {
	return &CouponServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

func (s *CouponServiceImpl) CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error {
	err := s.repo.CreateCoupon(ctx, coupon)
	if err != nil {
		s.logger.Error("Failed to create Coupon",
			zap.Any("Coupon", coupon),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *CouponServiceImpl) UpdateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error {
	err := s.repo.UpdateCoupon(ctx, coupon)
	if err != nil {
		s.logger.Error("Failed to update Coupon",
			zap.Any("Coupon", coupon),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *CouponServiceImpl) DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error {
	err := s.repo.DeleteCoupon(ctx, id)
	if err != nil {
		s.logger.Error("Failed to delete coupon",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *CouponServiceImpl) GetCouponDetailById(ctx *gin.Context, id uint64) (*entity.Coupon, *ecode.Error) {
	coupon, err := s.repo.GetCouponDetailById(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get Coupon from database",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return nil, err
	}
	return coupon, nil
}

func (s *CouponServiceImpl) GetCouponDetailBySlug(ctx *gin.Context, slug string) (*entity.Coupon, *ecode.Error) {
	coupon, err := s.repo.GetCouponDetailBySlug(ctx, slug)
	if err != nil {
		s.logger.Error("Failed to get Coupon from database",
			zap.String("slug", slug),
			zap.Error(err),
		)
		return nil, err
	}
	return coupon, nil
}

func (s *CouponServiceImpl) GetCouponDetailByCode(ctx *gin.Context, code string) (*entity.Coupon, *ecode.Error) {
	coupon, err := s.repo.GetCouponDetailByCode(ctx, code)
	if err != nil {
		s.logger.Error("Failed to get Coupon from database",
			zap.String("code", code),
			zap.Error(err),
		)
		return nil, err
	}
	return coupon, nil
}

func (s *CouponServiceImpl) GetCouponListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Coupon, int64, *ecode.Error) {
	coupons, total, err := s.repo.GetCouponListByCondition(ctx, condition)
	if err != nil {
		s.logger.Error("Failed to get Coupons from database",
			zap.Any("condition", condition),
			zap.Error(err),
		)
		return nil, 0, err
	}
	return coupons, total, nil
}

func (s *CouponServiceImpl) GetCouponCount(ctx *gin.Context) (int64, *ecode.Error) {
	total, err := s.repo.GetCouponCount(ctx)
	if err != nil {
		s.logger.Error("Failed to get Coupon count",
			zap.Error(err),
		)
		return 0, err
	}
	return total, nil
}
