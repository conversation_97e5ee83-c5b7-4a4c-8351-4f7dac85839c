package repository

import (
	"brandreviews/domain/brand/entity"
	"brandreviews/infra/ecode"

	"github.com/gin-gonic/gin"
)

type BrandRepository interface {
	CreateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error
	UpdateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error
	DeleteBrand(ctx *gin.Context, id uint64) *ecode.Error
	GetBrandDetailById(ctx *gin.Context, id uint64) (*entity.Brand, *ecode.Error)
	GetBrandListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Brand, int64, *ecode.Error)
	GetBrandDetailBySlug(ctx *gin.Context, slug string) (*entity.Brand, *ecode.Error)
	GetBrandCount(ctx *gin.Context) (int64, *ecode.Error)
}
