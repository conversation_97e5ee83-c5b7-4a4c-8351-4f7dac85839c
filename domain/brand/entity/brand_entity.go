package entity

import (
	"time"
)

type Brand struct {
	Id          uint64 `json:"id" gorm:"primaryKey"`
	Slug        string `json:"slug" gorm:"uniqueIndex;not null"`
	Name        string `json:"name" gorm:"type:varchar(255);not null"`
	Description string `json:"description" gorm:"type:text"`
	Logo        string `json:"logo" gorm:"type:varchar(255)"`
	Website     string `json:"website" gorm:"type:varchar(255)"`
	Featured    bool   `json:"featured" gorm:"type:boolean;default:false"`
	Active      bool   `json:"active" gorm:"type:boolean;default:true"`

	// Category association (no foreign key for performance)
	CategoryId   uint64 `json:"category_id"`
	CategorySlug string `json:"category_slug" gorm:"type:varchar(255)"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
