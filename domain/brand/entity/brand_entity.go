package entity

import (
	"time"
)

// Brand represents a brand entity with optimized field ordering for memory alignment
type Brand struct {
	// 8-byte aligned fields first
	Id         uint64    `json:"id" gorm:"primaryKey;index:idx_brand_id"`
	CategoryId uint64    `json:"category_id" gorm:"index:idx_brand_category"`
	CreatedAt  time.Time `json:"created_at" gorm:"index:idx_brand_created"`
	UpdatedAt  time.Time `json:"updated_at"`

	// String fields (pointer size - 8 bytes on 64-bit)
	Slug          string `json:"slug" gorm:"uniqueIndex:idx_brand_slug;type:varchar(100);not null" validate:"required,min=3,max=100,slug"`
	Name          string `json:"name" gorm:"type:varchar(255);not null;index:idx_brand_name" validate:"required,min=2,max=255"`
	Description   string `json:"description" gorm:"type:text" validate:"max=2000"`
	Logo          string `json:"logo" gorm:"type:varchar(500)" validate:"omitempty,url,max=500"`
	Website       string `json:"website" gorm:"type:varchar(500)" validate:"omitempty,url,max=500"`
	AffiliateLink string `json:"affiliate_link" gorm:"type:varchar(1000)" validate:"omitempty,url,max=1000"`
	CategorySlug  string `json:"category_slug" gorm:"type:varchar(100);index:idx_brand_category_slug" validate:"omitempty,min=3,max=100"`

	// Boolean fields (1 byte each, grouped together for better packing)
	Featured bool `json:"featured" gorm:"type:boolean;default:false;index:idx_brand_featured"`
	Active   bool `json:"active" gorm:"type:boolean;default:true;index:idx_brand_active"`
}

// TableName returns the table name for Brand entity
func (Brand) TableName() string {
	return "brands"
}
