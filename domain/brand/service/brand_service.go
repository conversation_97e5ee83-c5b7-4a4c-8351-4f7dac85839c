package service

import (
	"brandreviews/domain/brand/entity"
	"brandreviews/domain/brand/repository"
	"brandreviews/infra/ecode"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type BrandService interface {
	CreateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error
	UpdateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error
	DeleteBrand(ctx *gin.Context, id uint64) *ecode.Error
	GetBrandDetailById(ctx *gin.Context, id uint64) (*entity.Brand, *ecode.Error)
	GetBrandListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Brand, int64, *ecode.Error)
	GetBrandDetailBySlug(ctx *gin.Context, slug string) (*entity.Brand, *ecode.Error)
	GetBrandCount(ctx *gin.Context) (int64, *ecode.Error)
}

type BrandServiceImpl struct {
	repo   repository.BrandRepository
	logger *zap.Logger
}

func NewBrandService(repo repository.BrandRepository, logger *zap.Logger) BrandService {
	return &BrandServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

func (s *BrandServiceImpl) CreateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error {
	err := s.repo.CreateBrand(ctx, brand)
	if err != nil {
		s.logger.Error("Failed to create Brand",
			zap.Any("Brand", brand),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *BrandServiceImpl) UpdateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error {
	err := s.repo.UpdateBrand(ctx, brand)
	if err != nil {
		s.logger.Error("Failed to update Brand",
			zap.Any("Brand", brand),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *BrandServiceImpl) DeleteBrand(ctx *gin.Context, id uint64) *ecode.Error {
	err := s.repo.DeleteBrand(ctx, id)
	if err != nil {
		s.logger.Error("Failed to delete brand",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *BrandServiceImpl) GetBrandDetailById(ctx *gin.Context, id uint64) (*entity.Brand, *ecode.Error) {
	brand, err := s.repo.GetBrandDetailById(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get Brand from database",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return nil, err
	}
	return brand, nil
}

func (s *BrandServiceImpl) GetBrandDetailBySlug(ctx *gin.Context, slug string) (*entity.Brand, *ecode.Error) {
	brand, err := s.repo.GetBrandDetailBySlug(ctx, slug)
	if err != nil {
		s.logger.Error("Failed to get Brand from database",
			zap.String("slug", slug),
			zap.Error(err),
		)
		return nil, err
	}
	return brand, nil
}

func (s *BrandServiceImpl) GetBrandListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Brand, int64, *ecode.Error) {
	brands, total, err := s.repo.GetBrandListByCondition(ctx, condition)
	if err != nil {
		s.logger.Error("Failed to get Brands from database",
			zap.Any("condition", condition),
			zap.Error(err),
		)
		return nil, 0, err
	}
	return brands, total, nil
}

func (s *BrandServiceImpl) GetBrandCount(ctx *gin.Context) (int64, *ecode.Error) {
	total, err := s.repo.GetBrandCount(ctx)
	if err != nil {
		s.logger.Error("Failed to get Brand count",
			zap.Error(err),
		)
		return 0, err
	}
	return total, nil
}
