package service

import (
	"brandreviews/domain/tag/entity"
	"brandreviews/domain/tag/repository"
	"brandreviews/infra/ecode"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type TagService interface {
	CreateTag(ctx *gin.Context, tag *entity.Tag) *ecode.Error
	UpdateTag(ctx *gin.Context, tag *entity.Tag) *ecode.Error
	DeleteTag(ctx *gin.Context, id uint64) *ecode.Error
	GetTagDetailById(ctx *gin.Context, id uint64) (*entity.Tag, *ecode.Error)
	GetTagListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Tag, int64, *ecode.Error)
	GetTagCount(ctx *gin.Context) (int64, *ecode.Error)
}

type TagServiceImpl struct {
	repo   repository.TagRepository
	logger *zap.Logger
}

func NewTagService(repo repository.TagRepository, logger *zap.Logger) TagService {
	return &TagServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

func (s *TagServiceImpl) CreateTag(ctx *gin.Context, tag *entity.Tag) *ecode.Error {
	// 创建 Tag
	err := s.repo.CreateTag(ctx, tag)
	if err != nil {
		s.logger.Error("Failed to create Tag",
			zap.Any("Tag", tag),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *TagServiceImpl) UpdateTag(ctx *gin.Context, tag *entity.Tag) *ecode.Error {
	// 更新商家
	err := s.repo.UpdateTag(ctx, tag)
	if err != nil {
		s.logger.Error("Failed to update Tag",
			zap.Any("Tag", tag),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *TagServiceImpl) DeleteTag(ctx *gin.Context, id uint64) *ecode.Error {
	// 删除商家
	err := s.repo.DeleteTag(ctx, id)
	if err != nil {
		s.logger.Error("Failed to delete tag",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return err
	}

	return nil
}

func (s *TagServiceImpl) GetTagDetailById(ctx *gin.Context, id uint64) (*entity.Tag, *ecode.Error) {
	tag, err := s.repo.GetTagDetailById(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get Tag from database",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return nil, err
	}
	return tag, nil
}

func (s *TagServiceImpl) GetTagDetailBySlug(ctx *gin.Context, slug string) (*entity.Tag, *ecode.Error) {
	tag, err := s.repo.GetTagDetailBySlug(ctx, slug)
	if err != nil {
		s.logger.Error("Failed to get Tag from database",
			zap.String("slug", slug),
			zap.Error(err),
		)
		return nil, err
	}
	return tag, nil
}

func (s *TagServiceImpl) GetTagListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Tag, int64, *ecode.Error) {
	tags, total, err := s.repo.GetTagListByCondition(ctx, condition)
	if err != nil {
		s.logger.Error("Failed to get Tags from database",
			zap.Any("condition", condition),
			zap.Error(err),
		)
		return nil, 0, err
	}
	return tags, total, nil
}

func (s *TagServiceImpl) GetTagCount(ctx *gin.Context) (int64, *ecode.Error) {
	// 更新商家状态
	total, err := s.repo.GetTagCount(ctx)
	if err != nil {
		s.logger.Error("Failed to get Tag count",
			zap.Error(err),
		)
		return 0, err
	}

	return total, nil
}
