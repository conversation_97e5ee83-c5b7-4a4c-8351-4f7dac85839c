package repository

import (
	"brandreviews/domain/tag/entity"
	"brandreviews/infra/ecode"
	"github.com/gin-gonic/gin"
)

type TagRepository interface {
	CreateTag(ctx *gin.Context, tag *entity.Tag) *ecode.Error
	UpdateTag(ctx *gin.Context, tag *entity.Tag) *ecode.Error
	DeleteTag(ctx *gin.Context, id uint64) *ecode.Error
	GetTagDetailById(ctx *gin.Context, id uint64) (*entity.Tag, *ecode.Error)
	GetTagListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Tag, int64, *ecode.Error)
	GetTagDetailBySlug(ctx *gin.Context, slug string) (*entity.Tag, *ecode.Error)
	GetTagCount(ctx *gin.Context) (int64, *ecode.Error)
}
