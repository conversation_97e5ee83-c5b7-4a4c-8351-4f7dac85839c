package service

import (
	"brandreviews/domain/category/entity"
	"brandreviews/domain/category/repository"
	"brandreviews/infra/ecode"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CategoryService interface {
	CreateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error
	UpdateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error
	DeleteCategory(ctx *gin.Context, id uint64) *ecode.Error
	GetCategoryDetailById(ctx *gin.Context, id uint64) (*entity.Category, *ecode.Error)
	GetCategoryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Category, int64, *ecode.Error)
	GetCategoryCount(ctx *gin.Context) (int64, *ecode.Error)
}

type CategoryServiceImpl struct {
	repo   repository.CategoryRepository
	logger *zap.Logger
}

func NewCategoryService(repo repository.CategoryRepository, logger *zap.Logger) CategoryService {
	return &CategoryServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

func (s *CategoryServiceImpl) CreateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error {
	// 创建 Category
	err := s.repo.CreateCategory(ctx, category)
	if err != nil {
		s.logger.Error("Failed to create category",
			zap.Any("Category", category),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *CategoryServiceImpl) UpdateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error {
	err := s.repo.UpdateCategory(ctx, category)
	if err != nil {
		s.logger.Error("Failed to update category",
			zap.Any("Category", category),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *CategoryServiceImpl) DeleteCategory(ctx *gin.Context, id uint64) *ecode.Error {
	// 删除商家
	err := s.repo.DeleteCategory(ctx, id)
	if err != nil {
		s.logger.Error("Failed to delete Category",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return err
	}

	return nil
}

func (s *CategoryServiceImpl) GetCategoryDetailById(ctx *gin.Context, id uint64) (*entity.Category, *ecode.Error) {
	category, err := s.repo.GetCategoryDetailById(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get Category from database",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return nil, err
	}
	return category, nil
}

func (s *CategoryServiceImpl) GetCategoryDetailBySlug(ctx *gin.Context, slug string) (*entity.Category, *ecode.Error) {
	category, err := s.repo.GetCategoryDetailBySlug(ctx, slug)
	if err != nil {
		s.logger.Error("Failed to get Category from database",
			zap.String("slug", slug),
			zap.Error(err),
		)
		return nil, err
	}
	return category, nil
}

func (s *CategoryServiceImpl) GetCategoryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Category, int64, *ecode.Error) {
	categories, total, err := s.repo.GetCategoryListByCondition(ctx, condition)
	if err != nil {
		s.logger.Error("Failed to get categories from database",
			zap.Any("condition", condition),
			zap.Error(err),
		)
		return nil, 0, err
	}
	return categories, total, nil
}

func (s *CategoryServiceImpl) GetCategoryCount(ctx *gin.Context) (int64, *ecode.Error) {
	// 更新商家状态
	total, err := s.repo.GetCategoryCount(ctx)
	if err != nil {
		s.logger.Error("Failed to get Category count",
			zap.Error(err),
		)
		return 0, err
	}

	return total, nil
}
