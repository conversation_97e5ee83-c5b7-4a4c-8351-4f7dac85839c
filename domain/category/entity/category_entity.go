package entity

import "time"

type Category struct {
	Id          uint64    `json:"id" gorm:"primaryKey"`
	Slug        string    `json:"slug" gorm:"uniqueIndex;not null"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Icon        string    `json:"icon" gorm:"type:text"`
	Description string    `json:"description" gorm:"type:text"`
	Featured    bool      `json:"featured" gorm:"type:boolean;default:false"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}
