package repository

import (
	"brandreviews/domain/category/entity"
	"brandreviews/infra/ecode"
	"github.com/gin-gonic/gin"
)

type CategoryRepository interface {
	CreateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error
	UpdateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error
	DeleteCategory(ctx *gin.Context, id uint64) *ecode.Error
	GetCategoryDetailById(ctx *gin.Context, id uint64) (*entity.Category, *ecode.Error)
	GetCategoryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Category, int64, *ecode.Error)
	GetCategoryDetailBySlug(ctx *gin.Context, slug string) (*entity.Category, *ecode.Error)
	GetCategoryCount(ctx *gin.Context) (int64, *ecode.Error)
}
