package repository

import (
	"brandreviews/domain/article/entity"
	"brandreviews/infra/ecode"
	"github.com/gin-gonic/gin"
)

// ArticleRepository 博客仓储接口
type ArticleRepository interface {
	// CreateArticle 创建博客
	CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	// UpdateArticle 更新博客
	UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	// DeleteArticle 删除博客
	DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error
	// GetArticleDetailById 根据ID查找博客
	GetArticleDetailById(ctx *gin.Context, id uint64) (*entity.Article, *ecode.Error)
	// GetArticleListByCondition 搜索博客
	GetArticleListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Article, int64, *ecode.Error)

	GetArticleDetailBySlug(ctx *gin.Context, slug string) (*entity.Article, *ecode.Error)

	GetArticleCount(ctx *gin.Context) (int64, *ecode.Error)
}
