package entity

import (
	"time"

	"github.com/lib/pq"
)

// Article represents an article entity with optimized field ordering for memory alignment
type Article struct {
	// 8-byte aligned fields first
	Id           uint64    `json:"id" gorm:"primaryKey;index:idx_article_id"`
	BrandId      uint64    `json:"brand_id" gorm:"index:idx_article_brand"`
	CategoryId   uint64    `json:"category_id" gorm:"index:idx_article_category"`
	PublishedAt  uint64    `json:"published_at" gorm:"index:idx_article_published"`
	ViewCount    uint64    `json:"view_count" gorm:"type:bigint;default:0;index:idx_article_views"`
	CreatedAt    time.Time `json:"created_at" gorm:"index:idx_article_created"`
	UpdatedAt    time.Time `json:"updated_at"`
	LastViewedAt time.Time `json:"last_viewed_at" gorm:"index:idx_article_last_viewed"`

	// Array fields (slice header - 24 bytes on 64-bit)
	DealIds pq.Int64Array `json:"deal_ids" gorm:"type:bigint[]"`
	TagIds  pq.Int64Array `json:"tag_ids" gorm:"type:bigint[]"`

	// String fields (pointer size - 8 bytes on 64-bit)
	Slug          string `json:"slug" gorm:"uniqueIndex:idx_article_slug;type:varchar(100);not null" validate:"required,min=3,max=100,slug"`
	Title         string `json:"title" gorm:"type:varchar(300);not null;index:idx_article_title" validate:"required,min=10,max=300"`
	Description   string `json:"description" gorm:"type:text" validate:"max=500"`
	Content       string `json:"content" gorm:"type:text;not null" validate:"required,min=100"`
	FeaturedImage string `json:"featured_image" gorm:"type:varchar(500)" validate:"omitempty,url,max=500"`
	MetaTitle     string `json:"meta_title" gorm:"type:varchar(70)" validate:"omitempty,max=70"`
	MetaDesc      string `json:"meta_description" gorm:"type:varchar(160)" validate:"omitempty,max=160"`
	BrandSlug     string `json:"brand_slug" gorm:"type:varchar(100);index:idx_article_brand_slug" validate:"omitempty,min=3,max=100"`
	CategorySlug  string `json:"category_slug" gorm:"type:varchar(100);index:idx_article_category_slug" validate:"omitempty,min=3,max=100"`
	AuthorName    string `json:"author_name" gorm:"type:varchar(100)" validate:"omitempty,max=100"`
	LastUpdatedBy string `json:"last_updated_by" gorm:"type:varchar(100)" validate:"omitempty,max=100"`

	// Boolean fields (1 byte each, grouped together for better packing)
	Featured  bool `json:"featured" gorm:"type:boolean;default:false;index:idx_article_featured"`
	Published bool `json:"published" gorm:"type:boolean;default:false;index:idx_article_published_status"`
	Trending  bool `json:"trending" gorm:"type:boolean;default:false;index:idx_article_trending"`
}

// TableName returns the table name for Article entity
func (Article) TableName() string {
	return "articles"
}

// IsPublished checks if the article is published and visible
func (a *Article) IsPublished() bool {
	return a.Published && a.PublishedAt > 0 && a.PublishedAt <= uint64(time.Now().Unix())
}

// IncrementViewCount safely increments the view count
func (a *Article) IncrementViewCount() {
	a.ViewCount++
	a.LastViewedAt = time.Now()
}
