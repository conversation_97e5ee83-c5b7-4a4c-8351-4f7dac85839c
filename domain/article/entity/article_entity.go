package entity

import (
	"github.com/lib/pq"
	"time"
)

type Article struct {
	Id            uint64 `json:"id" gorm:"primaryKey"`
	Slug          string `json:"slug" gorm:"uniqueIndex;not null"`
	Title         string `json:"title" gorm:"type:varchar(255);not null"`
	Description   string `json:"description" gorm:"type:text"`
	Content       string `json:"content" gorm:"type:text;not null"`
	Featured      bool   `json:"featured" gorm:"type:boolean;default:false"`
	FeaturedImage string `json:"featured_image" gorm:"type:varchar(255)"`
	PublishedAt   uint64 `json:"published_at"`

	CategoryId   uint64        `json:"category_id" gorm:"type:varchar(255)"`
	CategorySlug string        `json:"category_slug" gorm:"type:varchar(255)"`
	DealIds      pq.Int64Array `json:"deal_ids" gorm:"type:text[]"`
	TagIds       pq.Int64Array `json:"tag_ids" gorm:"type:text[]"`
	BrandId      uint64        `json:"brand_id"`
	BrandSlug    string        `json:"brand_slug" gorm:"type:varchar(255)"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
