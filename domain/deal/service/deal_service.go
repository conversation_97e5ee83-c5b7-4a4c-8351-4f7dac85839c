package service

import (
	"brandreviews/domain/deal/entity"
	"brandreviews/domain/deal/repository"
	"brandreviews/infra/ecode"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DealService interface {
	CreateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error
	UpdateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error
	DeleteDeal(ctx *gin.Context, id uint64) *ecode.Error
	GetDealDetailById(ctx *gin.Context, id uint64) (*entity.Deal, *ecode.Error)
	GetDealListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Deal, int64, *ecode.Error)
	GetDealDetailBySlug(ctx *gin.Context, slug string) (*entity.Deal, *ecode.Error)
	GetDealCount(ctx *gin.Context) (int64, *ecode.Error)
}

type DealServiceImpl struct {
	repo   repository.DealRepository
	logger *zap.Logger
}

func NewDealService(repo repository.DealRepository, logger *zap.Logger) DealService {
	return &DealServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

func (s *DealServiceImpl) CreateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error {
	err := s.repo.CreateDeal(ctx, deal)
	if err != nil {
		s.logger.Error("Failed to create Deal",
			zap.Any("Deal", deal),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *DealServiceImpl) UpdateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error {
	err := s.repo.UpdateDeal(ctx, deal)
	if err != nil {
		s.logger.Error("Failed to update Deal",
			zap.Any("Deal", deal),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *DealServiceImpl) DeleteDeal(ctx *gin.Context, id uint64) *ecode.Error {
	err := s.repo.DeleteDeal(ctx, id)
	if err != nil {
		s.logger.Error("Failed to delete deal",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *DealServiceImpl) GetDealDetailById(ctx *gin.Context, id uint64) (*entity.Deal, *ecode.Error) {
	deal, err := s.repo.GetDealDetailById(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get Deal from database",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return nil, err
	}
	return deal, nil
}

func (s *DealServiceImpl) GetDealDetailBySlug(ctx *gin.Context, slug string) (*entity.Deal, *ecode.Error) {
	deal, err := s.repo.GetDealDetailBySlug(ctx, slug)
	if err != nil {
		s.logger.Error("Failed to get Deal from database",
			zap.String("slug", slug),
			zap.Error(err),
		)
		return nil, err
	}
	return deal, nil
}

func (s *DealServiceImpl) GetDealListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Deal, int64, *ecode.Error) {
	deals, total, err := s.repo.GetDealListByCondition(ctx, condition)
	if err != nil {
		s.logger.Error("Failed to get Deals from database",
			zap.Any("condition", condition),
			zap.Error(err),
		)
		return nil, 0, err
	}
	return deals, total, nil
}

func (s *DealServiceImpl) GetDealCount(ctx *gin.Context) (int64, *ecode.Error) {
	total, err := s.repo.GetDealCount(ctx)
	if err != nil {
		s.logger.Error("Failed to get Deal count",
			zap.Error(err),
		)
		return 0, err
	}
	return total, nil
}
