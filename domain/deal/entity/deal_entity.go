package entity

import (
	"time"
)

type Deal struct {
	Id          uint64 `json:"id" gorm:"primaryKey"`
	Slug        string `json:"slug" gorm:"uniqueIndex;not null"`
	Title       string `json:"title" gorm:"type:varchar(255);not null"`
	Description string `json:"description" gorm:"type:text"`
	DealUrl     string `json:"deal_url" gorm:"type:varchar(500);not null"`
	ImageUrl    string `json:"image_url" gorm:"type:varchar(255)"`

	// Pricing information
	OriginalPrice float64 `json:"original_price" gorm:"type:decimal(10,2)"`
	SalePrice     float64 `json:"sale_price" gorm:"type:decimal(10,2)"`
	DiscountType  string  `json:"discount_type" gorm:"type:varchar(50)"` // percentage, fixed, free_shipping
	DiscountValue float64 `json:"discount_value" gorm:"type:decimal(10,2)"`

	// Status and visibility
	Featured bool `json:"featured" gorm:"type:boolean;default:false"`
	Active   bool `json:"active" gorm:"type:boolean;default:true"`
	Verified bool `json:"verified" gorm:"type:boolean;default:false"`

	// Time constraints
	StartDate uint64 `json:"start_date"`
	EndDate   uint64 `json:"end_date"`

	// Brand association (no foreign key for performance)
	BrandId   uint64 `json:"brand_id"`
	BrandSlug string `json:"brand_slug" gorm:"type:varchar(255)"`

	// Category association (derived from brand)
	CategoryId   uint64 `json:"category_id"`
	CategorySlug string `json:"category_slug" gorm:"type:varchar(255)"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
