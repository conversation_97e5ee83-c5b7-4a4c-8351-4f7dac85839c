package entity

import (
	"time"
)

// Deal represents a deal entity with optimized field ordering for memory alignment
type Deal struct {
	// 8-byte aligned fields first
	Id            uint64    `json:"id" gorm:"primaryKey;index:idx_deal_id"`
	BrandId       uint64    `json:"brand_id" gorm:"index:idx_deal_brand"`
	CategoryId    uint64    `json:"category_id" gorm:"index:idx_deal_category"`
	StartDate     uint64    `json:"start_date" gorm:"index:idx_deal_start_date"`
	EndDate       uint64    `json:"end_date" gorm:"index:idx_deal_end_date"`
	OriginalPrice float64   `json:"original_price" gorm:"type:decimal(12,2)" validate:"omitempty,min=0"`
	SalePrice     float64   `json:"sale_price" gorm:"type:decimal(12,2)" validate:"omitempty,min=0"`
	DiscountValue float64   `json:"discount_value" gorm:"type:decimal(12,2)" validate:"omitempty,min=0"`
	CreatedAt     time.Time `json:"created_at" gorm:"index:idx_deal_created"`
	UpdatedAt     time.Time `json:"updated_at"`

	// String fields (pointer size - 8 bytes on 64-bit)
	Slug         string `json:"slug" gorm:"uniqueIndex:idx_deal_slug;type:varchar(100);not null" validate:"required,min=3,max=100,slug"`
	Title        string `json:"title" gorm:"type:varchar(255);not null;index:idx_deal_title" validate:"required,min=5,max=255"`
	Description  string `json:"description" gorm:"type:text" validate:"max=2000"`
	DealUrl      string `json:"deal_url" gorm:"type:varchar(1000);not null" validate:"required,url,max=1000"`
	ImageUrl     string `json:"image_url" gorm:"type:varchar(500)" validate:"omitempty,url,max=500"`
	DiscountType string `json:"discount_type" gorm:"type:varchar(20);index:idx_deal_discount_type" validate:"omitempty,oneof=percentage fixed free_shipping"`
	BrandSlug    string `json:"brand_slug" gorm:"type:varchar(100);index:idx_deal_brand_slug" validate:"omitempty,min=3,max=100"`
	CategorySlug string `json:"category_slug" gorm:"type:varchar(100);index:idx_deal_category_slug" validate:"omitempty,min=3,max=100"`

	// Boolean fields (1 byte each, grouped together for better packing)
	Featured bool `json:"featured" gorm:"type:boolean;default:false;index:idx_deal_featured"`
	Active   bool `json:"active" gorm:"type:boolean;default:true;index:idx_deal_active"`
	Verified bool `json:"verified" gorm:"type:boolean;default:false;index:idx_deal_verified"`
}

// TableName returns the table name for Deal entity
func (Deal) TableName() string {
	return "deals"
}

// IsValid checks if the deal is currently valid based on time constraints
func (d *Deal) IsValid() bool {
	if !d.Active {
		return false
	}
	now := uint64(time.Now().Unix())
	return (d.StartDate == 0 || d.StartDate <= now) && (d.EndDate == 0 || d.EndDate >= now)
}
