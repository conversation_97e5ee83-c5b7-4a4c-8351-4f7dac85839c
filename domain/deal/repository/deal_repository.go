package repository

import (
	"brandreviews/domain/deal/entity"
	"brandreviews/infra/ecode"

	"github.com/gin-gonic/gin"
)

type DealRepository interface {
	CreateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error
	UpdateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error
	DeleteDeal(ctx *gin.Context, id uint64) *ecode.Error
	GetDealDetailById(ctx *gin.Context, id uint64) (*entity.Deal, *ecode.Error)
	GetDealListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Deal, int64, *ecode.Error)
	GetDealDetailBySlug(ctx *gin.Context, slug string) (*entity.Deal, *ecode.Error)
	GetDealCount(ctx *gin.Context) (int64, *ecode.Error)
}
