//go:build wireinject
// +build wireinject

package main

import (
	brandapp "brandreviews/application/brand/appservice"
	categoryapp "brandreviews/application/category/appservice"
	couponapp "brandreviews/application/coupon/appservice"
	dealapp "brandreviews/application/deal/appservice"
	tagapp "brandreviews/application/tag/appservice"
	"brandreviews/config"
	brandservice "brandreviews/domain/brand/service"
	categoryservice "brandreviews/domain/category/service"
	couponservice "brandreviews/domain/coupon/service"
	dealservice "brandreviews/domain/deal/service"
	tagservice "brandreviews/domain/tag/service"
	"brandreviews/infra/dao"
	"brandreviews/infra/database"
	"brandreviews/infra/logger"
	"brandreviews/interfaces/api/handler/brand"
	"brandreviews/interfaces/api/handler/category"
	"brandreviews/interfaces/api/handler/coupon"
	"brandreviews/interfaces/api/handler/deal"
	"brandreviews/interfaces/api/handler/tag"
	"brandreviews/interfaces/api/router"

	"gorm.io/gorm"

	"github.com/google/wire"
	"go.uber.org/zap"
)

var ProviderSet = wire.NewSet(
	// 基础设施
	ProvideLogger,
	ProvidePostgres,

	// Repositories
	dao.NewTagPostgresRepository,
	dao.NewCategoryPostgresRepository,
	dao.NewBrandPostgresRepository,
	dao.NewDealPostgresRepository,
	dao.NewCouponPostgresRepository,

	// Domain Services
	tagservice.NewTagService,
	categoryservice.NewCategoryService,
	brandservice.NewBrandService,
	dealservice.NewDealService,
	couponservice.NewCouponService,

	// Application Services
	tagapp.NewTagAppService,
	categoryapp.NewCategoryAppService,
	brandapp.NewBrandAppService,
	dealapp.NewDealAppService,
	couponapp.NewCouponAppService,

	// Handlers
	tag.NewTagHandler,
	category.NewCategoryHandler,
	brand.NewBrandHandler,
	deal.NewDealHandler,
	coupon.NewCouponHandler,

	// 路由
	router.NewApiServer,
)

type Application struct {
	Router     *router.ApiServer
	Logger     *zap.Logger
	Config     *config.Config
	PostgresDB *gorm.DB
}

// InitializeApplication 初始化应用程序
func InitializeApplication(cfg *config.Config) (*Application, error) {
	wire.Build(
		ProviderSet,
		wire.Struct(new(Application), "*"),
	)
	return nil, nil
}

// ProvidePostgres 提供 PostgreSQL 连接
func ProvidePostgres(cfg *config.Config) (*gorm.DB, error) {
	return database.NewPostgresDB(cfg.Postgres)
}

// ProvideLogger 提供日志记录器
func ProvideLogger(cfg *config.Config) (*zap.Logger, error) {
	return logger.NewLogger(cfg.Logger)
}

// ProvideApplication 提供应用程序
func ProvideApplication(router *router.ApiServer, logger *zap.Logger) *Application {
	return &Application{
		Router: router,
		Logger: logger,
	}
}
