package main

import (
	"brandreviews/config"
	"context"
	"flag"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"go.uber.org/zap"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"
)

func main() {
	// 获取环境配置的优先级：
	// 1. 命令行参数
	// 2. 环境变量
	// 3. .env 文件
	// 4. 默认值

	var env string
	flag.StringVar(&env, "env", "", "environment (local, test, live)")
	flag.Parse()

	// 如果没有通过命令行指定环境，则尝试从环境变量获取
	if env == "" {
		env = os.Getenv("APP_ENV")
	}

	// 如果环境变量也没有设置，则尝试加载 .env 文件
	if env == "" {
		// 获取项目根目录
		rootDir := findProjectRoot()

		// 加载 env 文件
		if err := godotenv.Load(filepath.Join(rootDir, "env")); err != nil {
			log.Printf("Warning: Error loading env file: %v", err)
		}

		env = os.Getenv("APP_ENV")
	}

	// 如果所有方式都没有设置环境，则使用默认值
	if env == "" {
		env = "local"
	}

	// 加载配置
	cfg, err := config.LoadConfig(env)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 使用 Wire 初始化应用程序
	app, err := InitializeApplication(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize application: %v", err)
	}
	defer app.Logger.Sync()

	// 设置 Gin 模式
	if env == "live" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 初始化 Gin 路由
	r := gin.New()
	r.Use(gin.Logger(), gin.Recovery())

	// 设置路由
	app.Router.Setup(r)

	// 创建上下文和取消函数
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 设置信号处理
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	// 创建 HTTP 服务器
	addr := fmt.Sprintf("%s:%d", cfg.API.Host, cfg.API.Port)
	srv := &http.Server{
		Addr:    addr,
		Handler: r,
	}

	// 在 goroutine 中启动服务器
	go func() {
		app.Logger.Info("Starting API server", zap.String("addr", addr))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			app.Logger.Error("Server error", zap.Error(err))
			cancel()
		}
	}()

	// 等待中断信号
	<-signalChan
	app.Logger.Info("Received shutdown signal")

	// 创建关闭超时上下文
	shutdownCtx, shutdownCancel := context.WithTimeout(ctx, 10*time.Second)
	defer shutdownCancel()

	// 优雅关闭服务器
	if err := srv.Shutdown(shutdownCtx); err != nil {
		app.Logger.Error("Server forced to shutdown", zap.Error(err))
	}

	app.Logger.Info("Server exiting")
}

// findProjectRoot 查找项目根目录
func findProjectRoot() string {
	// 获取当前工作目录
	dir, err := os.Getwd()
	if err != nil {
		return ""
	}

	// 向上遍历直到找到包含 go.mod 的目录
	for {
		if _, err := os.Stat(filepath.Join(dir, "go.mod")); err == nil {
			return dir
		}

		parentDir := filepath.Dir(dir)
		if parentDir == dir {
			return ""
		}
		dir = parentDir
	}
}
