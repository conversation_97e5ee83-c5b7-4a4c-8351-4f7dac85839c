// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	appservice3 "brandreviews/application/brand/appservice"
	appservice2 "brandreviews/application/category/appservice"
	appservice5 "brandreviews/application/coupon/appservice"
	appservice4 "brandreviews/application/deal/appservice"
	"brandreviews/application/tag/appservice"
	"brandreviews/config"
	service3 "brandreviews/domain/brand/service"
	service2 "brandreviews/domain/category/service"
	service5 "brandreviews/domain/coupon/service"
	service4 "brandreviews/domain/deal/service"
	"brandreviews/domain/tag/service"
	"brandreviews/infra/dao"
	"brandreviews/infra/database"
	"brandreviews/infra/logger"
	"brandreviews/interfaces/api/handler/brand"
	"brandreviews/interfaces/api/handler/category"
	"brandreviews/interfaces/api/handler/coupon"
	"brandreviews/interfaces/api/handler/deal"
	"brandreviews/interfaces/api/handler/tag"
	"brandreviews/interfaces/api/router"
	"github.com/google/wire"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Injectors from wire.go:

// InitializeApplication 初始化应用程序
func InitializeApplication(cfg *config.Config) (*Application, error) {
	db, err := ProvidePostgres(cfg)
	if err != nil {
		return nil, err
	}
	tagRepository := dao.NewTagPostgresRepository(db)
	logger, err := ProvideLogger(cfg)
	if err != nil {
		return nil, err
	}
	tagService := service.NewTagService(tagRepository, logger)
	tagAppService := appservice.NewTagAppService(tagService)
	tagHandler := tag.NewTagHandler(tagAppService, logger)
	categoryRepository := dao.NewCategoryPostgresRepository(db)
	categoryService := service2.NewCategoryService(categoryRepository, logger)
	categoryAppService := appservice2.NewCategoryAppService(categoryService)
	categoryHandler := category.NewCategoryHandler(categoryAppService, logger)
	brandRepository := dao.NewBrandPostgresRepository(db)
	brandService := service3.NewBrandService(brandRepository, logger)
	brandAppService := appservice3.NewBrandAppService(brandService)
	brandHandler := brand.NewBrandHandler(brandAppService, logger)
	dealRepository := dao.NewDealPostgresRepository(db)
	dealService := service4.NewDealService(dealRepository, logger)
	dealAppService := appservice4.NewDealAppService(dealService)
	dealHandler := deal.NewDealHandler(dealAppService, logger)
	couponRepository := dao.NewCouponPostgresRepository(db)
	couponService := service5.NewCouponService(couponRepository, logger)
	couponAppService := appservice5.NewCouponAppService(couponService)
	couponHandler := coupon.NewCouponHandler(couponAppService, logger)
	apiServer := router.NewApiServer(tagHandler, categoryHandler, brandHandler, dealHandler, couponHandler)
	application := &Application{
		Router:     apiServer,
		Logger:     logger,
		Config:     cfg,
		PostgresDB: db,
	}
	return application, nil
}

// wire.go:

var ProviderSet = wire.NewSet(

	ProvideLogger,
	ProvidePostgres, dao.NewTagPostgresRepository, dao.NewCategoryPostgresRepository, dao.NewBrandPostgresRepository, dao.NewDealPostgresRepository, dao.NewCouponPostgresRepository, service.NewTagService, service2.NewCategoryService, service3.NewBrandService, service4.NewDealService, service5.NewCouponService, appservice.NewTagAppService, appservice2.NewCategoryAppService, appservice3.NewBrandAppService, appservice4.NewDealAppService, appservice5.NewCouponAppService, tag.NewTagHandler, category.NewCategoryHandler, brand.NewBrandHandler, deal.NewDealHandler, coupon.NewCouponHandler, router.NewApiServer,
)

type Application struct {
	Router     *router.ApiServer
	Logger     *zap.Logger
	Config     *config.Config
	PostgresDB *gorm.DB
}

// ProvidePostgres 提供 PostgreSQL 连接
func ProvidePostgres(cfg *config.Config) (*gorm.DB, error) {
	return database.NewPostgresDB(cfg.Postgres)
}

// ProvideLogger 提供日志记录器
func ProvideLogger(cfg *config.Config) (*zap.Logger, error) {
	return logger.NewLogger(cfg.Logger)
}

// ProvideApplication 提供应用程序
func ProvideApplication(router2 *router.ApiServer, logger2 *zap.Logger) *Application {
	return &Application{
		Router: router2,
		Logger: logger2,
	}
}
