// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	appservice2 "brandreviews/application/category/appservice"
	"brandreviews/application/tag/appservice"
	"brandreviews/config"
	service2 "brandreviews/domain/category/service"
	"brandreviews/domain/tag/service"
	"brandreviews/infra/dao"
	"brandreviews/infra/database"
	"brandreviews/infra/logger"
	"brandreviews/interfaces/api/handler/category"
	"brandreviews/interfaces/api/handler/tag"
	"brandreviews/interfaces/api/router"
	"github.com/google/wire"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Injectors from wire.go:

// InitializeApplication 初始化应用程序
func InitializeApplication(cfg *config.Config) (*Application, error) {
	db, err := ProvidePostgres(cfg)
	if err != nil {
		return nil, err
	}
	tagRepository := dao.NewTagPostgresRepository(db)
	logger, err := ProvideLogger(cfg)
	if err != nil {
		return nil, err
	}
	tagService := service.NewTagService(tagRepository, logger)
	tagAppService := appservice.NewTagAppService(tagService)
	tagHandler := tag.NewTagHandler(tagAppService, logger)
	categoryRepository := dao.NewCategoryPostgresRepository(db)
	categoryService := service2.NewCategoryService(categoryRepository, logger)
	categoryAppService := appservice2.NewCategoryAppService(categoryService)
	categoryHandler := category.NewCategoryHandler(categoryAppService, logger)
	apiServer := router.NewApiServer(tagHandler, categoryHandler)
	application := &Application{
		Router:     apiServer,
		Logger:     logger,
		Config:     cfg,
		PostgresDB: db,
	}
	return application, nil
}

// wire.go:

var ProviderSet = wire.NewSet(

	ProvideLogger,
	ProvidePostgres, dao.NewTagPostgresRepository, dao.NewCategoryPostgresRepository, service.NewTagService, service2.NewCategoryService, appservice.NewTagAppService, appservice2.NewCategoryAppService, tag.NewTagHandler, category.NewCategoryHandler, router.NewApiServer,
)

type Application struct {
	Router     *router.ApiServer
	Logger     *zap.Logger
	Config     *config.Config
	PostgresDB *gorm.DB
}

// ProvidePostgres 提供 PostgreSQL 连接
func ProvidePostgres(cfg *config.Config) (*gorm.DB, error) {
	return database.NewPostgresDB(cfg.Postgres)
}

// ProvideLogger 提供日志记录器
func ProvideLogger(cfg *config.Config) (*zap.Logger, error) {
	return logger.NewLogger(cfg.Logger)
}

// ProvideApplication 提供应用程序
func ProvideApplication(router2 *router.ApiServer, logger2 *zap.Logger) *Application {
	return &Application{
		Router: router2,
		Logger: logger2,
	}
}
