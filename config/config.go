package config

import (
	"fmt"
	"github.com/spf13/viper"
	"os"
	"path/filepath"
)

type Config struct {
	API      APIConfig      `mapstructure:"api"`
	Task     TaskConfig     `mapstructure:"task"`
	Postgres PostgresConfig `mapstructure:"postgres"`
	Logger   LoggerConfig   `mapstructure:"logger"`
}

type APIConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	ReadTimeout  string `mapstructure:"read_timeout"`
	WriteTimeout string `mapstructure:"write_timeout"`
}

type TaskConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	ReadTimeout  string `mapstructure:"read_timeout"`
	WriteTimeout string `mapstructure:"write_timeout"`
}

type PostgresConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	DBName   string `mapstructure:"dbname"`
	SSLMode  string `mapstructure:"sslmode"`
	TimeZone string `mapstructure:"timezone"`
}

type LoggerConfig struct {
	Level            string   `mapstructure:"level"`
	Encoding         string   `mapstructure:"encoding"`
	OutputPaths      []string `mapstructure:"output_paths"`
	ErrorOutputPaths []string `mapstructure:"error_output_paths"`
}

// LoadConfig 加载配置文件
// env 参数指定环境：local, test, live
func LoadConfig(env string) (*Config, error) {
	// 设置配置文件路径
	configPath := filepath.Join("configs", env)
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("config directory not found: %s", configPath)
	}

	// 初始化 viper
	v := viper.New()
	v.SetConfigType("yaml")
	v.AddConfigPath(configPath)

	// 加载 common 配置
	v.SetConfigName("common")
	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("error reading common config: %w", err)
	}

	// 加载 api 配置
	v.SetConfigName("api")
	if err := v.MergeInConfig(); err != nil {
		return nil, fmt.Errorf("error reading api config: %w", err)
	}

	// 加载 task 配置
	v.SetConfigName("task")
	if err := v.MergeInConfig(); err != nil {
		return nil, fmt.Errorf("error reading task config: %w", err)
	}

	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("error unmarshaling config: %w", err)
	}

	return &config, nil
}
