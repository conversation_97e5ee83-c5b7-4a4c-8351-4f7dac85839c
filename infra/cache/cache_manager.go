package cache

import (
	"fmt"
	"time"
)

// CacheManager manages multiple cache layers and provides cache warming
type CacheManager struct {
	l1Cache *MemoryCache // Fast in-memory cache
	l2Cache *MemoryCache // Application-level cache with longer TTL
}

// CacheKey constants for different data types
const (
	// Brand cache keys
	BrandByIDKey    = "brand:id:%d"
	BrandBySlugKey  = "brand:slug:%s"
	BrandListKey    = "brand:list:%s" // hash of conditions
	FeaturedBrands  = "brands:featured"
	
	// Deal cache keys
	DealByIDKey     = "deal:id:%d"
	DealBySlugKey   = "deal:slug:%s"
	DealListKey     = "deal:list:%s"
	FeaturedDeals   = "deals:featured"
	ValidDeals      = "deals:valid"
	
	// Coupon cache keys
	CouponByIDKey   = "coupon:id:%d"
	CouponBySlugKey = "coupon:slug:%s"
	CouponByCodeKey = "coupon:code:%s"
	CouponListKey   = "coupon:list:%s"
	FeaturedCoupons = "coupons:featured"
	ValidCoupons    = "coupons:valid"
	
	// Article cache keys
	ArticleByIDKey   = "article:id:%d"
	ArticleBySlugKey = "article:slug:%s"
	ArticleListKey   = "article:list:%s"
	FeaturedArticles = "articles:featured"
	TrendingArticles = "articles:trending"
	
	// Category cache keys
	CategoryByIDKey   = "category:id:%d"
	CategoryBySlugKey = "category:slug:%s"
	CategoryListKey   = "categories:list"
	
	// Tag cache keys
	TagByIDKey   = "tag:id:%d"
	TagBySlugKey = "tag:slug:%s"
	TagListKey   = "tags:list"
	
	// Statistics cache keys
	StatsKey = "stats:%s"
)

// Cache TTL configurations
var (
	ShortTTL  = 5 * time.Minute   // For frequently changing data
	MediumTTL = 15 * time.Minute  // For moderately stable data
	LongTTL   = 1 * time.Hour     // For stable data
	VeryLongTTL = 24 * time.Hour  // For very stable data like categories
)

// NewCacheManager creates a new cache manager with L1 and L2 caches
func NewCacheManager() *CacheManager {
	l1Config := &CacheConfig{
		DefaultTTL:      MediumTTL,
		CleanupInterval: 2 * time.Minute,
		MaxItems:        5000,
	}
	
	l2Config := &CacheConfig{
		DefaultTTL:      LongTTL,
		CleanupInterval: 10 * time.Minute,
		MaxItems:        20000,
	}
	
	return &CacheManager{
		l1Cache: NewMemoryCache(l1Config),
		l2Cache: NewMemoryCache(l2Config),
	}
}

// Get retrieves value from L1 cache, falls back to L2 cache
func (cm *CacheManager) Get(key string) (interface{}, bool) {
	// Try L1 cache first
	if value, exists := cm.l1Cache.Get(key); exists {
		return value, true
	}
	
	// Try L2 cache
	if value, exists := cm.l2Cache.Get(key); exists {
		// Promote to L1 cache
		cm.l1Cache.SetWithTTL(key, value, ShortTTL)
		return value, true
	}
	
	return nil, false
}

// Set stores value in both L1 and L2 caches
func (cm *CacheManager) Set(key string, value interface{}, ttl time.Duration) {
	cm.l1Cache.SetWithTTL(key, value, ttl)
	cm.l2Cache.SetWithTTL(key, value, ttl*2) // L2 cache has longer TTL
}

// SetL1Only stores value only in L1 cache (for frequently accessed data)
func (cm *CacheManager) SetL1Only(key string, value interface{}, ttl time.Duration) {
	cm.l1Cache.SetWithTTL(key, value, ttl)
}

// SetL2Only stores value only in L2 cache (for less frequently accessed data)
func (cm *CacheManager) SetL2Only(key string, value interface{}, ttl time.Duration) {
	cm.l2Cache.SetWithTTL(key, value, ttl)
}

// GetJSON retrieves and unmarshals JSON value
func (cm *CacheManager) GetJSON(key string, dest interface{}) bool {
	if cm.l1Cache.GetJSON(key, dest) {
		return true
	}
	
	if cm.l2Cache.GetJSON(key, dest) {
		// Promote to L1 cache
		cm.l1Cache.SetJSONWithTTL(key, dest, ShortTTL)
		return true
	}
	
	return false
}

// SetJSON marshals and stores JSON value
func (cm *CacheManager) SetJSON(key string, value interface{}, ttl time.Duration) error {
	if err := cm.l1Cache.SetJSONWithTTL(key, value, ttl); err != nil {
		return err
	}
	return cm.l2Cache.SetJSONWithTTL(key, value, ttl*2)
}

// Delete removes value from both caches
func (cm *CacheManager) Delete(key string) {
	cm.l1Cache.Delete(key)
	cm.l2Cache.Delete(key)
}

// DeletePattern removes all keys matching pattern
func (cm *CacheManager) DeletePattern(pattern string) {
	// Simple pattern matching for cache invalidation
	l1Keys := cm.l1Cache.Keys()
	l2Keys := cm.l2Cache.Keys()
	
	for _, key := range l1Keys {
		if matchesPattern(key, pattern) {
			cm.l1Cache.Delete(key)
		}
	}
	
	for _, key := range l2Keys {
		if matchesPattern(key, pattern) {
			cm.l2Cache.Delete(key)
		}
	}
}

// matchesPattern performs simple pattern matching
func matchesPattern(key, pattern string) bool {
	// Simple implementation - can be enhanced with regex if needed
	if pattern == "*" {
		return true
	}
	
	// Check if pattern is a prefix
	if len(pattern) > 0 && pattern[len(pattern)-1] == '*' {
		prefix := pattern[:len(pattern)-1]
		return len(key) >= len(prefix) && key[:len(prefix)] == prefix
	}
	
	return key == pattern
}

// InvalidateBrand invalidates all brand-related cache entries
func (cm *CacheManager) InvalidateBrand(brandID uint64, brandSlug string) {
	cm.Delete(fmt.Sprintf(BrandByIDKey, brandID))
	if brandSlug != "" {
		cm.Delete(fmt.Sprintf(BrandBySlugKey, brandSlug))
	}
	cm.DeletePattern("brand:list:*")
	cm.Delete(FeaturedBrands)
}

// InvalidateDeal invalidates all deal-related cache entries
func (cm *CacheManager) InvalidateDeal(dealID uint64, dealSlug string) {
	cm.Delete(fmt.Sprintf(DealByIDKey, dealID))
	if dealSlug != "" {
		cm.Delete(fmt.Sprintf(DealBySlugKey, dealSlug))
	}
	cm.DeletePattern("deal:list:*")
	cm.Delete(FeaturedDeals)
	cm.Delete(ValidDeals)
}

// InvalidateCoupon invalidates all coupon-related cache entries
func (cm *CacheManager) InvalidateCoupon(couponID uint64, couponSlug, couponCode string) {
	cm.Delete(fmt.Sprintf(CouponByIDKey, couponID))
	if couponSlug != "" {
		cm.Delete(fmt.Sprintf(CouponBySlugKey, couponSlug))
	}
	if couponCode != "" {
		cm.Delete(fmt.Sprintf(CouponByCodeKey, couponCode))
	}
	cm.DeletePattern("coupon:list:*")
	cm.Delete(FeaturedCoupons)
	cm.Delete(ValidCoupons)
}

// InvalidateArticle invalidates all article-related cache entries
func (cm *CacheManager) InvalidateArticle(articleID uint64, articleSlug string) {
	cm.Delete(fmt.Sprintf(ArticleByIDKey, articleID))
	if articleSlug != "" {
		cm.Delete(fmt.Sprintf(ArticleBySlugKey, articleSlug))
	}
	cm.DeletePattern("article:list:*")
	cm.Delete(FeaturedArticles)
	cm.Delete(TrendingArticles)
}

// WarmupCache preloads frequently accessed data
func (cm *CacheManager) WarmupCache(warmupFn func(*CacheManager) error) error {
	return warmupFn(cm)
}

// GetStats returns combined cache statistics
func (cm *CacheManager) GetStats() map[string]interface{} {
	l1Stats := cm.l1Cache.GetStats()
	l2Stats := cm.l2Cache.GetStats()
	
	return map[string]interface{}{
		"l1_cache": map[string]interface{}{
			"hits":       l1Stats.Hits,
			"misses":     l1Stats.Misses,
			"hit_ratio":  cm.l1Cache.GetHitRatio(),
			"item_count": l1Stats.ItemCount,
			"sets":       l1Stats.Sets,
			"deletes":    l1Stats.Deletes,
			"evictions":  l1Stats.Evictions,
		},
		"l2_cache": map[string]interface{}{
			"hits":       l2Stats.Hits,
			"misses":     l2Stats.Misses,
			"hit_ratio":  cm.l2Cache.GetHitRatio(),
			"item_count": l2Stats.ItemCount,
			"sets":       l2Stats.Sets,
			"deletes":    l2Stats.Deletes,
			"evictions":  l2Stats.Evictions,
		},
		"total_hit_ratio": (cm.l1Cache.GetHitRatio() + cm.l2Cache.GetHitRatio()) / 2,
	}
}

// Clear clears both cache layers
func (cm *CacheManager) Clear() {
	cm.l1Cache.Clear()
	cm.l2Cache.Clear()
}

// Close closes both cache layers
func (cm *CacheManager) Close() {
	cm.l1Cache.Close()
	cm.l2Cache.Close()
}

// GetOrSetJSON retrieves JSON value or sets it using the provided function
func (cm *CacheManager) GetOrSetJSON(key string, dest interface{}, fn func() (interface{}, error), ttl time.Duration) error {
	if cm.GetJSON(key, dest) {
		return nil
	}
	
	value, err := fn()
	if err != nil {
		return err
	}
	
	return cm.SetJSON(key, value, ttl)
}
