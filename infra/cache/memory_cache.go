package cache

import (
	"encoding/json"
	"sync"
	"time"
)

// CacheItem represents a cached item with expiration
type CacheItem struct {
	Value     interface{}
	ExpiresAt time.Time
	CreatedAt time.Time
}

// IsExpired checks if the cache item has expired
func (c *CacheItem) IsExpired() bool {
	return time.Now().After(c.ExpiresAt)
}

// MemoryCache represents an in-memory cache with TTL support
type MemoryCache struct {
	items   sync.Map
	stats   *CacheStats
	config  *CacheConfig
	cleaner *time.Ticker
	done    chan bool
}

// CacheStats holds cache performance statistics
type CacheStats struct {
	mu          sync.RWMutex
	Hits        int64
	Misses      int64
	Sets        int64
	Deletes     int64
	Evictions   int64
	ItemCount   int64
	LastCleanup time.Time
}

// CacheConfig holds cache configuration
type CacheConfig struct {
	DefaultTTL      time.Duration
	CleanupInterval time.Duration
	MaxItems        int64
}

// DefaultCacheConfig returns default cache configuration
func DefaultCacheConfig() *CacheConfig {
	return &CacheConfig{
		DefaultTTL:      15 * time.Minute,
		CleanupInterval: 5 * time.Minute,
		MaxItems:        10000,
	}
}

// NewMemoryCache creates a new in-memory cache
func NewMemoryCache(config *CacheConfig) *MemoryCache {
	if config == nil {
		config = DefaultCacheConfig()
	}

	cache := &MemoryCache{
		items:  sync.Map{},
		stats:  &CacheStats{},
		config: config,
		done:   make(chan bool),
	}

	// Start cleanup goroutine
	cache.startCleanup()

	return cache
}

// startCleanup starts the background cleanup process
func (c *MemoryCache) startCleanup() {
	c.cleaner = time.NewTicker(c.config.CleanupInterval)
	go func() {
		for {
			select {
			case <-c.cleaner.C:
				c.cleanup()
			case <-c.done:
				c.cleaner.Stop()
				return
			}
		}
	}()
}

// cleanup removes expired items from cache
func (c *MemoryCache) cleanup() {
	now := time.Now()
	evicted := int64(0)

	c.items.Range(func(key, value interface{}) bool {
		if item, ok := value.(*CacheItem); ok && item.IsExpired() {
			c.items.Delete(key)
			evicted++
		}
		return true
	})

	c.stats.mu.Lock()
	c.stats.Evictions += evicted
	c.stats.ItemCount -= evicted
	c.stats.LastCleanup = now
	c.stats.mu.Unlock()
}

// Set stores a value in cache with default TTL
func (c *MemoryCache) Set(key string, value interface{}) {
	c.SetWithTTL(key, value, c.config.DefaultTTL)
}

// SetWithTTL stores a value in cache with custom TTL
func (c *MemoryCache) SetWithTTL(key string, value interface{}, ttl time.Duration) {
	item := &CacheItem{
		Value:     value,
		ExpiresAt: time.Now().Add(ttl),
		CreatedAt: time.Now(),
	}

	// Check if key already exists
	_, exists := c.items.Load(key)
	c.items.Store(key, item)

	c.stats.mu.Lock()
	c.stats.Sets++
	if !exists {
		c.stats.ItemCount++
	}
	c.stats.mu.Unlock()
}

// Get retrieves a value from cache
func (c *MemoryCache) Get(key string) (interface{}, bool) {
	value, exists := c.items.Load(key)
	if !exists {
		c.stats.mu.Lock()
		c.stats.Misses++
		c.stats.mu.Unlock()
		return nil, false
	}

	item := value.(*CacheItem)
	if item.IsExpired() {
		c.items.Delete(key)
		c.stats.mu.Lock()
		c.stats.Misses++
		c.stats.ItemCount--
		c.stats.mu.Unlock()
		return nil, false
	}

	c.stats.mu.Lock()
	c.stats.Hits++
	c.stats.mu.Unlock()

	return item.Value, true
}

// GetString retrieves a string value from cache
func (c *MemoryCache) GetString(key string) (string, bool) {
	value, exists := c.Get(key)
	if !exists {
		return "", false
	}

	if str, ok := value.(string); ok {
		return str, true
	}
	return "", false
}

// GetJSON retrieves and unmarshals JSON value from cache
func (c *MemoryCache) GetJSON(key string, dest interface{}) bool {
	value, exists := c.Get(key)
	if !exists {
		return false
	}

	if jsonData, ok := value.([]byte); ok {
		return json.Unmarshal(jsonData, dest) == nil
	}
	return false
}

// SetJSON marshals and stores JSON value in cache
func (c *MemoryCache) SetJSON(key string, value interface{}) error {
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}
	c.Set(key, jsonData)
	return nil
}

// SetJSONWithTTL marshals and stores JSON value in cache with custom TTL
func (c *MemoryCache) SetJSONWithTTL(key string, value interface{}, ttl time.Duration) error {
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}
	c.SetWithTTL(key, jsonData, ttl)
	return nil
}

// Delete removes a value from cache
func (c *MemoryCache) Delete(key string) {
	_, exists := c.items.LoadAndDelete(key)
	if exists {
		c.stats.mu.Lock()
		c.stats.Deletes++
		c.stats.ItemCount--
		c.stats.mu.Unlock()
	}
}

// Clear removes all items from cache
func (c *MemoryCache) Clear() {
	c.items.Range(func(key, value interface{}) bool {
		c.items.Delete(key)
		return true
	})

	c.stats.mu.Lock()
	c.stats.ItemCount = 0
	c.stats.mu.Unlock()
}

// GetStats returns cache statistics
func (c *MemoryCache) GetStats() CacheStats {
	c.stats.mu.RLock()
	defer c.stats.mu.RUnlock()
	return *c.stats
}

// GetHitRatio returns cache hit ratio
func (c *MemoryCache) GetHitRatio() float64 {
	stats := c.GetStats()
	total := stats.Hits + stats.Misses
	if total == 0 {
		return 0
	}
	return float64(stats.Hits) / float64(total)
}

// Close stops the cache cleanup process
func (c *MemoryCache) Close() {
	close(c.done)
	if c.cleaner != nil {
		c.cleaner.Stop()
	}
}

// Exists checks if a key exists in cache (without affecting stats)
func (c *MemoryCache) Exists(key string) bool {
	value, exists := c.items.Load(key)
	if !exists {
		return false
	}

	item := value.(*CacheItem)
	return !item.IsExpired()
}

// GetOrSet retrieves a value from cache or sets it using the provided function
func (c *MemoryCache) GetOrSet(key string, fn func() (interface{}, error)) (interface{}, error) {
	return c.GetOrSetWithTTL(key, fn, c.config.DefaultTTL)
}

// GetOrSetWithTTL retrieves a value from cache or sets it using the provided function with custom TTL
func (c *MemoryCache) GetOrSetWithTTL(key string, fn func() (interface{}, error), ttl time.Duration) (interface{}, error) {
	if value, exists := c.Get(key); exists {
		return value, nil
	}

	value, err := fn()
	if err != nil {
		return nil, err
	}

	c.SetWithTTL(key, value, ttl)
	return value, nil
}

// Keys returns all non-expired keys in cache
func (c *MemoryCache) Keys() []string {
	var keys []string
	c.items.Range(func(key, value interface{}) bool {
		if item, ok := value.(*CacheItem); ok && !item.IsExpired() {
			if keyStr, ok := key.(string); ok {
				keys = append(keys, keyStr)
			}
		}
		return true
	})
	return keys
}

// Size returns the number of items in cache
func (c *MemoryCache) Size() int64 {
	c.stats.mu.RLock()
	defer c.stats.mu.RUnlock()
	return c.stats.ItemCount
}
