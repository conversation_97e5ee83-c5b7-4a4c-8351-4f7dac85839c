package graceful

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"brandreviews/infra/cache"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ShutdownManager manages graceful shutdown of the application
type ShutdownManager struct {
	server       *http.Server
	db           *gorm.DB
	cacheManager *cache.CacheManager
	logger       *zap.Logger
	shutdownFuncs []func() error
	mu           sync.Mutex
	isShuttingDown bool
}

// NewShutdownManager creates a new shutdown manager
func NewShutdownManager(server *http.Server, db *gorm.DB, cacheManager *cache.CacheManager, logger *zap.Logger) *ShutdownManager {
	return &ShutdownManager{
		server:       server,
		db:           db,
		cacheManager: cacheManager,
		logger:       logger,
		shutdownFuncs: make([]func() error, 0),
	}
}

// RegisterShutdownFunc registers a function to be called during shutdown
func (sm *ShutdownManager) RegisterShutdownFunc(fn func() error) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	sm.shutdownFuncs = append(sm.shutdownFuncs, fn)
}

// IsShuttingDown returns true if the application is shutting down
func (sm *ShutdownManager) IsShuttingDown() bool {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	return sm.isShuttingDown
}

// WaitForShutdown waits for shutdown signals and performs graceful shutdown
func (sm *ShutdownManager) WaitForShutdown() {
	// Create a channel to receive OS signals
	sigChan := make(chan os.Signal, 1)
	
	// Register the channel to receive specific signals
	signal.Notify(sigChan, 
		syscall.SIGINT,  // Ctrl+C
		syscall.SIGTERM, // Termination signal
		syscall.SIGQUIT, // Quit signal
	)
	
	// Wait for signal
	sig := <-sigChan
	sm.logger.Info("Received shutdown signal", zap.String("signal", sig.String()))
	
	// Mark as shutting down
	sm.mu.Lock()
	sm.isShuttingDown = true
	sm.mu.Unlock()
	
	// Perform graceful shutdown
	sm.performShutdown()
}

// performShutdown performs the actual shutdown process
func (sm *ShutdownManager) performShutdown() {
	sm.logger.Info("Starting graceful shutdown...")
	
	// Create a context with timeout for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	// Channel to track shutdown completion
	done := make(chan bool, 1)
	
	go func() {
		defer func() {
			done <- true
		}()
		
		// Step 1: Stop accepting new requests
		sm.logger.Info("Stopping HTTP server...")
		if sm.server != nil {
			if err := sm.server.Shutdown(ctx); err != nil {
				sm.logger.Error("Error shutting down HTTP server", zap.Error(err))
			} else {
				sm.logger.Info("HTTP server stopped successfully")
			}
		}
		
		// Step 2: Execute custom shutdown functions
		sm.logger.Info("Executing custom shutdown functions...")
		sm.mu.Lock()
		shutdownFuncs := make([]func() error, len(sm.shutdownFuncs))
		copy(shutdownFuncs, sm.shutdownFuncs)
		sm.mu.Unlock()
		
		for i, fn := range shutdownFuncs {
			sm.logger.Info("Executing shutdown function", zap.Int("index", i))
			if err := fn(); err != nil {
				sm.logger.Error("Error executing shutdown function", 
					zap.Int("index", i), 
					zap.Error(err),
				)
			}
		}
		
		// Step 3: Close cache manager
		if sm.cacheManager != nil {
			sm.logger.Info("Closing cache manager...")
			sm.cacheManager.Close()
			sm.logger.Info("Cache manager closed")
		}
		
		// Step 4: Close database connections
		if sm.db != nil {
			sm.logger.Info("Closing database connections...")
			if sqlDB, err := sm.db.DB(); err == nil {
				if err := sqlDB.Close(); err != nil {
					sm.logger.Error("Error closing database", zap.Error(err))
				} else {
					sm.logger.Info("Database connections closed successfully")
				}
			}
		}
		
		sm.logger.Info("Graceful shutdown completed")
	}()
	
	// Wait for shutdown to complete or timeout
	select {
	case <-done:
		sm.logger.Info("Shutdown completed successfully")
	case <-ctx.Done():
		sm.logger.Warn("Shutdown timed out, forcing exit")
	}
}

// HealthCheckMiddleware returns a middleware that responds with 503 during shutdown
func (sm *ShutdownManager) HealthCheckMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if sm.IsShuttingDown() {
				w.WriteHeader(http.StatusServiceUnavailable)
				w.Write([]byte("Service is shutting down"))
				return
			}
			next.ServeHTTP(w, r)
		})
	}
}

// ShutdownConfig holds configuration for graceful shutdown
type ShutdownConfig struct {
	Timeout         time.Duration
	ShutdownMessage string
	HealthCheckPath string
}

// DefaultShutdownConfig returns default shutdown configuration
func DefaultShutdownConfig() *ShutdownConfig {
	return &ShutdownConfig{
		Timeout:         30 * time.Second,
		ShutdownMessage: "Service is shutting down",
		HealthCheckPath: "/health",
	}
}

// GracefulServer wraps an HTTP server with graceful shutdown capabilities
type GracefulServer struct {
	server    *http.Server
	manager   *ShutdownManager
	config    *ShutdownConfig
	logger    *zap.Logger
	startTime time.Time
}

// NewGracefulServer creates a new graceful server
func NewGracefulServer(
	server *http.Server,
	db *gorm.DB,
	cacheManager *cache.CacheManager,
	logger *zap.Logger,
	config *ShutdownConfig,
) *GracefulServer {
	if config == nil {
		config = DefaultShutdownConfig()
	}
	
	manager := NewShutdownManager(server, db, cacheManager, logger)
	
	return &GracefulServer{
		server:    server,
		manager:   manager,
		config:    config,
		logger:    logger,
		startTime: time.Now(),
	}
}

// Start starts the server and waits for shutdown signals
func (gs *GracefulServer) Start() error {
	// Start the server in a goroutine
	go func() {
		gs.logger.Info("Starting HTTP server", 
			zap.String("addr", gs.server.Addr),
			zap.Time("start_time", gs.startTime),
		)
		
		if err := gs.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			gs.logger.Fatal("Failed to start server", zap.Error(err))
		}
	}()
	
	gs.logger.Info("Server started successfully", zap.String("addr", gs.server.Addr))
	
	// Wait for shutdown signal
	gs.manager.WaitForShutdown()
	
	return nil
}

// RegisterShutdownFunc registers a function to be called during shutdown
func (gs *GracefulServer) RegisterShutdownFunc(fn func() error) {
	gs.manager.RegisterShutdownFunc(fn)
}

// IsShuttingDown returns true if the server is shutting down
func (gs *GracefulServer) IsShuttingDown() bool {
	return gs.manager.IsShuttingDown()
}

// GetUptime returns the server uptime
func (gs *GracefulServer) GetUptime() time.Duration {
	return time.Since(gs.startTime)
}

// Shutdown initiates graceful shutdown programmatically
func (gs *GracefulServer) Shutdown() {
	gs.logger.Info("Initiating programmatic shutdown")
	
	// Send SIGTERM to self to trigger graceful shutdown
	process, err := os.FindProcess(os.Getpid())
	if err != nil {
		gs.logger.Error("Failed to find current process", zap.Error(err))
		return
	}
	
	if err := process.Signal(syscall.SIGTERM); err != nil {
		gs.logger.Error("Failed to send SIGTERM signal", zap.Error(err))
	}
}

// ShutdownHook represents a function that should be called during shutdown
type ShutdownHook struct {
	Name string
	Func func() error
}

// ShutdownHooks manages a collection of shutdown hooks
type ShutdownHooks struct {
	hooks  []ShutdownHook
	logger *zap.Logger
	mu     sync.Mutex
}

// NewShutdownHooks creates a new shutdown hooks manager
func NewShutdownHooks(logger *zap.Logger) *ShutdownHooks {
	return &ShutdownHooks{
		hooks:  make([]ShutdownHook, 0),
		logger: logger,
	}
}

// Add adds a shutdown hook
func (sh *ShutdownHooks) Add(name string, fn func() error) {
	sh.mu.Lock()
	defer sh.mu.Unlock()
	
	sh.hooks = append(sh.hooks, ShutdownHook{
		Name: name,
		Func: fn,
	})
	
	sh.logger.Info("Registered shutdown hook", zap.String("name", name))
}

// Execute executes all shutdown hooks
func (sh *ShutdownHooks) Execute() {
	sh.mu.Lock()
	hooks := make([]ShutdownHook, len(sh.hooks))
	copy(hooks, sh.hooks)
	sh.mu.Unlock()
	
	sh.logger.Info("Executing shutdown hooks", zap.Int("count", len(hooks)))
	
	for _, hook := range hooks {
		sh.logger.Info("Executing shutdown hook", zap.String("name", hook.Name))
		
		if err := hook.Func(); err != nil {
			sh.logger.Error("Shutdown hook failed", 
				zap.String("name", hook.Name),
				zap.Error(err),
			)
		} else {
			sh.logger.Info("Shutdown hook completed", zap.String("name", hook.Name))
		}
	}
	
	sh.logger.Info("All shutdown hooks executed")
}

// Clear removes all shutdown hooks
func (sh *ShutdownHooks) Clear() {
	sh.mu.Lock()
	defer sh.mu.Unlock()
	
	sh.hooks = sh.hooks[:0]
	sh.logger.Info("Cleared all shutdown hooks")
}
