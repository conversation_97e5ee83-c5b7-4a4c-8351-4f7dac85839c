package dao

import (
	"brandreviews/domain/coupon/entity"
	"brandreviews/domain/coupon/repository"
	"brandreviews/infra/ecode"
	"errors"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CouponPostgresRepository struct {
	db *gorm.DB
}

// NewCouponPostgresRepository 创建优惠券仓储
func NewCouponPostgresRepository(db *gorm.DB) repository.CouponRepository {
	return &CouponPostgresRepository{db: db}
}

// CreateCoupon 创建优惠券
func (r *CouponPostgresRepository) CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(coupon).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// UpdateCoupon 更新优惠券
func (r *CouponPostgresRepository) UpdateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(coupon).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// DeleteCoupon 删除优惠券
func (r *CouponPostgresRepository) DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.Coupon{}, id).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// GetCouponDetailById 根据ID查找优惠券
func (r *CouponPostgresRepository) GetCouponDetailById(ctx *gin.Context, id uint64) (*entity.Coupon, *ecode.Error) {
	var coupon entity.Coupon
	if err := r.db.WithContext(ctx).First(&coupon, id).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &coupon, nil
}

func (r *CouponPostgresRepository) GetCouponDetailBySlug(ctx *gin.Context, slug string) (*entity.Coupon, *ecode.Error) {
	var coupon entity.Coupon
	if err := r.db.WithContext(ctx).Where("slug = ?", slug).First(&coupon).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &coupon, nil
}

func (r *CouponPostgresRepository) GetCouponDetailByCode(ctx *gin.Context, code string) (*entity.Coupon, *ecode.Error) {
	var coupon entity.Coupon
	if err := r.db.WithContext(ctx).Where("code = ?", code).First(&coupon).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &coupon, nil
}

// GetCouponListByCondition 搜索优惠券
func (r *CouponPostgresRepository) GetCouponListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Coupon, int64, *ecode.Error) {
	var coupons []*entity.Coupon
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Coupon{})

	// 处理搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ? OR code ILIKE ?", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if featured, ok := condition["featured"].(bool); ok {
		query = query.Where("featured = ?", featured)
	}

	if active, ok := condition["active"].(bool); ok {
		query = query.Where("active = ?", active)
	}

	if verified, ok := condition["verified"].(bool); ok {
		query = query.Where("verified = ?", verified)
	}

	if brandId, ok := condition["brand_id"].(uint64); ok && brandId > 0 {
		query = query.Where("brand_id = ?", brandId)
	}

	if categoryId, ok := condition["category_id"].(uint64); ok && categoryId > 0 {
		query = query.Where("category_id = ?", categoryId)
	}

	if discountType, ok := condition["discount_type"].(string); ok && discountType != "" {
		query = query.Where("discount_type = ?", discountType)
	}

	// 处理时间过滤 - 只显示有效的优惠券
	if validOnly, ok := condition["valid_only"].(bool); ok && validOnly {
		now := uint64(time.Now().Unix())
		query = query.Where("(start_date = 0 OR start_date <= ?) AND (end_date = 0 OR end_date >= ?)", now, now)
	}

	// 处理使用限制过滤
	if availableOnly, ok := condition["available_only"].(bool); ok && availableOnly {
		query = query.Where("(usage_limit = 0 OR used_count < usage_limit)")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 排序
	query = query.Order("featured DESC, verified DESC, created_at DESC")

	// 获取列表
	if err := query.Find(&coupons).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	return coupons, total, nil
}

func (r *CouponPostgresRepository) GetCouponCount(ctx *gin.Context) (int64, *ecode.Error) {
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&entity.Coupon{})

	// 在应用所有筛选条件后计算总数
	if err := query.Count(&total).Error; err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count Coupon")
	}
	return total, nil
}
