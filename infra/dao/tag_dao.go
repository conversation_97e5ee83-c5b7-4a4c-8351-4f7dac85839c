package dao

import (
	"brandreviews/domain/tag/entity"
	"brandreviews/domain/tag/repository"
	"brandreviews/infra/ecode"
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TagPostgresRepository struct {
	db *gorm.DB
}

// NewTagPostgresRepository 创建博客仓储
func NewTagPostgresRepository(db *gorm.DB) repository.TagRepository {
	return &TagPostgresRepository{db: db}
}

// CreateTag 创建博客
func (r *TagPostgresRepository) CreateTag(ctx *gin.Context, tag *entity.Tag) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(tag).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// UpdateTag 更新博客
func (r *TagPostgresRepository) UpdateTag(ctx *gin.Context, tag *entity.Tag) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(tag).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// DeleteTag 删除博客
func (r *TagPostgresRepository) DeleteTag(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.Tag{}, id).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// GetTagDetailById 根据ID查找博客
func (r *TagPostgresRepository) GetTagDetailById(ctx *gin.Context, id uint64) (*entity.Tag, *ecode.Error) {
	var tag entity.Tag
	if err := r.db.WithContext(ctx).First(&tag, id).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &tag, nil
}

func (r *TagPostgresRepository) GetTagDetailBySlug(ctx *gin.Context, slug string) (*entity.Tag, *ecode.Error) {
	var tag entity.Tag
	if err := r.db.WithContext(ctx).Where("slug = ?", slug).First(&tag).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &tag, nil
}

// GetTagListByCondition 搜索博客
func (r *TagPostgresRepository) GetTagListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Tag, int64, *ecode.Error) {
	var tags []*entity.Tag
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Tag{})

	// 处理搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		query = query.Where("name ILIKE ?", "%"+search+"%")
	}

	if featured, ok := condition["featured"].(bool); ok {
		query = query.Where("featured = ?", featured)
	}
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 获取列表
	if err := query.Find(&tags).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	return tags, total, nil
}

func (r *TagPostgresRepository) GetTagCount(ctx *gin.Context) (int64, *ecode.Error) {
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&entity.Tag{})

	// 在应用所有筛选条件后计算总数
	if err := query.Count(&total).Error; err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count Tag")
	}
	return total, nil
}
