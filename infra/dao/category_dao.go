package dao

import (
	"brandreviews/domain/category/entity"
	"brandreviews/domain/category/repository"
	"brandreviews/infra/ecode"
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CategoryPostgresRepository struct {
	db *gorm.DB
}

func NewCategoryPostgresRepository(db *gorm.DB) repository.CategoryRepository {
	return &CategoryPostgresRepository{db: db}
}

func (r *CategoryPostgresRepository) CreateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(category).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

func (r *CategoryPostgresRepository) UpdateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(category).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

func (r *CategoryPostgresRepository) DeleteCategory(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.Category{}, id).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

func (r *CategoryPostgresRepository) GetCategoryDetailById(ctx *gin.Context, id uint64) (*entity.Category, *ecode.Error) {
	var category entity.Category
	if err := r.db.WithContext(ctx).First(&category, id).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &category, nil
}

func (r *CategoryPostgresRepository) GetCategoryDetailBySlug(ctx *gin.Context, slug string) (*entity.Category, *ecode.Error) {
	var category entity.Category
	if err := r.db.WithContext(ctx).Where("slug = ?", slug).First(&category).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &category, nil
}

func (r *CategoryPostgresRepository) GetCategoryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Category, int64, *ecode.Error) {
	var categories []*entity.Category
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Category{})

	// 处理搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		query = query.Where("name ILIKE ?", "%"+search+"%")
	}

	if featured, ok := condition["featured"].(bool); ok {
		query = query.Where("featured = ?", featured)
	}
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 获取列表
	if err := query.Find(&categories).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	return categories, total, nil
}

func (r *CategoryPostgresRepository) GetCategoryCount(ctx *gin.Context) (int64, *ecode.Error) {
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&entity.Category{})

	// 在应用所有筛选条件后计算总数
	if err := query.Count(&total).Error; err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count Category")
	}
	return total, nil
}
