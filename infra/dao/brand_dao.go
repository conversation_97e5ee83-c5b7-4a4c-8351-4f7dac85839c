package dao

import (
	"brandreviews/domain/brand/entity"
	"brandreviews/domain/brand/repository"
	"brandreviews/infra/ecode"
	"errors"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type BrandPostgresRepository struct {
	db *gorm.DB
}

// NewBrandPostgresRepository 创建品牌仓储
func NewBrandPostgresRepository(db *gorm.DB) repository.BrandRepository {
	return &BrandPostgresRepository{db: db}
}

// CreateBrand 创建品牌
func (r *BrandPostgresRepository) CreateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(brand).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// UpdateBrand 更新品牌
func (r *BrandPostgresRepository) UpdateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(brand).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// DeleteBrand 删除品牌
func (r *BrandPostgresRepository) DeleteBrand(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.Brand{}, id).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// GetBrandDetailById 根据ID查找品牌
func (r *BrandPostgresRepository) GetBrandDetailById(ctx *gin.Context, id uint64) (*entity.Brand, *ecode.Error) {
	var brand entity.Brand
	if err := r.db.WithContext(ctx).First(&brand, id).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &brand, nil
}

func (r *BrandPostgresRepository) GetBrandDetailBySlug(ctx *gin.Context, slug string) (*entity.Brand, *ecode.Error) {
	var brand entity.Brand
	if err := r.db.WithContext(ctx).Where("slug = ?", slug).First(&brand).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &brand, nil
}

// GetBrandListByCondition 搜索品牌
func (r *BrandPostgresRepository) GetBrandListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Brand, int64, *ecode.Error) {
	var brands []*entity.Brand
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Brand{})

	// 处理搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if featured, ok := condition["featured"].(bool); ok {
		query = query.Where("featured = ?", featured)
	}

	if active, ok := condition["active"].(bool); ok {
		query = query.Where("active = ?", active)
	}

	if categoryId, ok := condition["category_id"].(uint64); ok && categoryId > 0 {
		query = query.Where("category_id = ?", categoryId)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 排序
	query = query.Order("featured DESC, created_at DESC")

	// 获取列表
	if err := query.Find(&brands).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	return brands, total, nil
}

func (r *BrandPostgresRepository) GetBrandCount(ctx *gin.Context) (int64, *ecode.Error) {
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&entity.Brand{})

	// 在应用所有筛选条件后计算总数
	if err := query.Count(&total).Error; err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count Brand")
	}
	return total, nil
}
