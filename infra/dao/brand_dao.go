package dao

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"brandreviews/domain/brand/entity"
	"brandreviews/domain/brand/repository"
	"brandreviews/infra/cache"
	"brandreviews/infra/ecode"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type BrandPostgresRepository struct {
	db           *gorm.DB
	cacheManager *cache.CacheManager
	logger       *zap.Logger
}

// NewBrandPostgresRepository 创建品牌仓储
func NewBrandPostgresRepository(db *gorm.DB, cacheManager *cache.CacheManager, logger *zap.Logger) repository.BrandRepository {
	return &BrandPostgresRepository{
		db:           db,
		cacheManager: cacheManager,
		logger:       logger,
	}
}

// CreateBrand 创建品牌
func (r *BrandPostgresRepository) CreateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error {
	// Add context timeout
	dbCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	if err := r.db.WithContext(dbCtx).Create(brand).Error; err != nil {
		r.logger.Error("Failed to create brand", zap.Error(err), zap.Any("brand", brand))
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create brand")
	}

	// Invalidate related cache entries
	if r.cacheManager != nil {
		r.cacheManager.InvalidateBrand(brand.Id, brand.Slug)
	}

	return nil
}

// UpdateBrand 更新品牌
func (r *BrandPostgresRepository) UpdateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(brand).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// DeleteBrand 删除品牌
func (r *BrandPostgresRepository) DeleteBrand(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.Brand{}, id).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// GetBrandDetailById 根据ID查找品牌
func (r *BrandPostgresRepository) GetBrandDetailById(ctx *gin.Context, id uint64) (*entity.Brand, *ecode.Error) {
	// Try cache first
	if r.cacheManager != nil {
		cacheKey := fmt.Sprintf(cache.BrandByIDKey, id)
		var brand entity.Brand
		if r.cacheManager.GetJSON(cacheKey, &brand) {
			return &brand, nil
		}
	}

	// Add context timeout
	dbCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	var brand entity.Brand
	if err := r.db.WithContext(dbCtx).First(&brand, id).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		r.logger.Error("Failed to get brand by ID", zap.Error(err), zap.Uint64("id", id))
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get brand by ID")
	}

	// Cache the result
	if r.cacheManager != nil {
		cacheKey := fmt.Sprintf(cache.BrandByIDKey, id)
		r.cacheManager.SetJSON(cacheKey, &brand, cache.MediumTTL)
	}

	return &brand, nil
}

func (r *BrandPostgresRepository) GetBrandDetailBySlug(ctx *gin.Context, slug string) (*entity.Brand, *ecode.Error) {
	var brand entity.Brand
	if err := r.db.WithContext(ctx).Where("slug = ?", slug).First(&brand).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &brand, nil
}

// GetBrandListByCondition 搜索品牌
func (r *BrandPostgresRepository) GetBrandListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Brand, int64, *ecode.Error) {
	// Generate cache key from condition
	var cacheKey string
	if r.cacheManager != nil {
		conditionBytes, _ := json.Marshal(condition)
		hash := md5.Sum(conditionBytes)
		cacheKey = fmt.Sprintf(cache.BrandListKey, fmt.Sprintf("%x", hash))

		// Try cache first
		type CachedResult struct {
			Brands []*entity.Brand `json:"brands"`
			Total  int64           `json:"total"`
		}
		var cached CachedResult
		if r.cacheManager.GetJSON(cacheKey, &cached) {
			return cached.Brands, cached.Total, nil
		}
	}

	// Add context timeout
	dbCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	var brands []*entity.Brand
	var total int64

	query := r.db.WithContext(dbCtx).Model(&entity.Brand{})

	// 处理搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if featured, ok := condition["featured"].(bool); ok {
		query = query.Where("featured = ?", featured)
	}

	if active, ok := condition["active"].(bool); ok {
		query = query.Where("active = ?", active)
	}

	if categoryId, ok := condition["category_id"].(uint64); ok && categoryId > 0 {
		query = query.Where("category_id = ?", categoryId)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Failed to count brands", zap.Error(err), zap.Any("condition", condition))
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count brands")
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 排序
	query = query.Order("featured DESC, created_at DESC")

	// 获取列表
	if err := query.Find(&brands).Error; err != nil {
		r.logger.Error("Failed to get brand list", zap.Error(err), zap.Any("condition", condition))
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get brand list")
	}

	// Cache the result
	if r.cacheManager != nil && cacheKey != "" {
		result := struct {
			Brands []*entity.Brand `json:"brands"`
			Total  int64           `json:"total"`
		}{
			Brands: brands,
			Total:  total,
		}
		r.cacheManager.SetJSON(cacheKey, result, cache.ShortTTL)
	}

	return brands, total, nil
}

func (r *BrandPostgresRepository) GetBrandCount(ctx *gin.Context) (int64, *ecode.Error) {
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&entity.Brand{})

	// 在应用所有筛选条件后计算总数
	if err := query.Count(&total).Error; err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count Brand")
	}
	return total, nil
}
