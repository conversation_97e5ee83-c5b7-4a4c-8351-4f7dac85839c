package dao

import (
	"brandreviews/domain/article/entity"
	"brandreviews/domain/article/repository"
	"brandreviews/infra/ecode"
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type articlePostgresRepository struct {
	db *gorm.DB
}

// NewArticlePostgresRepository 创建博客仓储
func NewArticlePostgresRepository(db *gorm.DB) repository.ArticleRepository {
	return &articlePostgresRepository{db: db}
}

// CreateArticle 创建博客
func (r *articlePostgresRepository) CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(article).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// UpdateArticle 更新博客
func (r *articlePostgresRepository) UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(article).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// DeleteArticle 删除博客
func (r *articlePostgresRepository) DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.Article{}, id).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// GetArticleDetailById 根据ID查找博客
func (r *articlePostgresRepository) GetArticleDetailById(ctx *gin.Context, id uint64) (*entity.Article, *ecode.Error) {
	var article entity.Article
	if err := r.db.WithContext(ctx).First(&article, id).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &article, nil
}

func (r *articlePostgresRepository) GetArticleDetailBySlug(ctx *gin.Context, slug string) (*entity.Article, *ecode.Error) {
	var article entity.Article
	if err := r.db.WithContext(ctx).Where("slug = ?", slug).First(&article).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &article, nil
}

// GetArticleListByCondition 搜索博客
func (r *articlePostgresRepository) GetArticleListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Article, int64, *ecode.Error) {
	var article []*entity.Article
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Article{})

	// 处理搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		query = query.Where("title ILIKE ? OR content ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if category, ok := condition["category"].(string); ok && category != "" {
		query = query.Where("category_slug = ?", category)
	}
	if brand, ok := condition["brand"].(string); ok && brand != "" {
		query = query.Where("brand_slug = ?", brand)
	}
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 获取列表
	if err := query.Find(&article).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	return article, total, nil
}

func (r *articlePostgresRepository) GetArticleCount(ctx *gin.Context) (int64, *ecode.Error) {
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&entity.Article{})

	// 在应用所有筛选条件后计算总数
	if err := query.Count(&total).Error; err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count Article")
	}
	return total, nil
}
