package dao

import (
	"brandreviews/domain/deal/entity"
	"brandreviews/domain/deal/repository"
	"brandreviews/infra/ecode"
	"errors"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type DealPostgresRepository struct {
	db *gorm.DB
}

// NewDealPostgresRepository 创建优惠仓储
func NewDealPostgresRepository(db *gorm.DB) repository.DealRepository {
	return &DealPostgresRepository{db: db}
}

// CreateDeal 创建优惠
func (r *DealPostgresRepository) CreateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(deal).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// UpdateDeal 更新优惠
func (r *DealPostgresRepository) UpdateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(deal).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// DeleteDeal 删除优惠
func (r *DealPostgresRepository) DeleteDeal(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.Deal{}, id).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// GetDealDetailById 根据ID查找优惠
func (r *DealPostgresRepository) GetDealDetailById(ctx *gin.Context, id uint64) (*entity.Deal, *ecode.Error) {
	var deal entity.Deal
	if err := r.db.WithContext(ctx).First(&deal, id).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &deal, nil
}

func (r *DealPostgresRepository) GetDealDetailBySlug(ctx *gin.Context, slug string) (*entity.Deal, *ecode.Error) {
	var deal entity.Deal
	if err := r.db.WithContext(ctx).Where("slug = ?", slug).First(&deal).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &deal, nil
}

// GetDealListByCondition 搜索优惠
func (r *DealPostgresRepository) GetDealListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Deal, int64, *ecode.Error) {
	var deals []*entity.Deal
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Deal{})

	// 处理搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if featured, ok := condition["featured"].(bool); ok {
		query = query.Where("featured = ?", featured)
	}

	if active, ok := condition["active"].(bool); ok {
		query = query.Where("active = ?", active)
	}

	if verified, ok := condition["verified"].(bool); ok {
		query = query.Where("verified = ?", verified)
	}

	if brandId, ok := condition["brand_id"].(uint64); ok && brandId > 0 {
		query = query.Where("brand_id = ?", brandId)
	}

	if categoryId, ok := condition["category_id"].(uint64); ok && categoryId > 0 {
		query = query.Where("category_id = ?", categoryId)
	}

	// 处理时间过滤 - 只显示有效的优惠
	if validOnly, ok := condition["valid_only"].(bool); ok && validOnly {
		now := uint64(time.Now().Unix())
		query = query.Where("(start_date = 0 OR start_date <= ?) AND (end_date = 0 OR end_date >= ?)", now, now)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 排序
	query = query.Order("featured DESC, verified DESC, created_at DESC")

	// 获取列表
	if err := query.Find(&deals).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	return deals, total, nil
}

func (r *DealPostgresRepository) GetDealCount(ctx *gin.Context) (int64, *ecode.Error) {
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&entity.Deal{})

	// 在应用所有筛选条件后计算总数
	if err := query.Count(&total).Error; err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count Deal")
	}
	return total, nil
}
