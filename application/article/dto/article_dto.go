package dto

import (
	"github.com/lib/pq"
	"time"
)

// GetArticleListReq 获取博客列表请求
type GetArticleListReq struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=10" binding:"min=1,max=100"`
	Category string `form:"category"`
	Search   string `form:"search"`
	Brand    string `form:"brand"`
	Tag      string `form:"tag"`
}

// ArticleDetailResp 博客信息
type ArticleDetailResp struct {
	Id   uint64 `json:"id"`
	Slug string `json:"slug"`

	Title         string        `json:"title"`
	Description   string        `json:"description"`
	Content       string        `json:"content"`
	FeaturedImage string        `json:"featured_image"`
	PublishedAt   time.Time     `json:"published_at"`
	CategoryInfo  string        `json:"category_info"`
	DealList      pq.Int64Array `json:"deal_list"`
	TagList       pq.Int64Array `json:"tag_list"`
	BrandInfo     uint64        `json:"brand_info"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ArticleListResp 获取博客列表响应
type ArticleListResp struct {
	Total       int64                `json:"total"`
	Page        int                  `json:"page"`
	PageSize    int                  `json:"page_size"`
	ArticleList []*ArticleDetailResp `json:"article_list"`
}

func (req *GetArticleListReq) Dto2ConditionGetArticleList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Category != "" {
		condition["category"] = req.Category
	}
	if req.Brand != "" {
		condition["brand"] = req.Brand
	}
	return condition
}

//
//func Entity2DtoArticleDetailResp(blog *entity.Article, merchant *merchantentity.Merchant) (resp *BlogDetailResp) {
//	resp = &BlogDetailResp{}
//	resp.ID = blog.ID
//	resp.Title = blog.Title
//	resp.Description = blog.Description
//	resp.Content = blog.Content
//	resp.FeaturedImage = blog.FeaturedImage
//	resp.PublishedAt = blog.PublishedAt
//	resp.Category = blog.Category
//	resp.Tags = blog.Tags
//	if merchant != nil {
//		resp.MerchantInfo = dto.Entity2DtoMerchantDetailResp(merchant, nil)
//	} else {
//		resp.MerchantInfo = &dto.MerchantDetailResp{}
//	}
//	return resp
//}
//
//func Entity2DtoArticleListResp(pageSize int, page int, total int64, blogList []*entity.Blog, merchantMap map[uint64]*merchantentity.Merchant) (resp *BlogListResp) {
//	// 获取列表时可以接受没有商家信息
//	resp = &BlogListResp{}
//	resp.Total = total
//	resp.PageSize = pageSize
//	resp.Page = page
//	for i := range blogList {
//		if merchant, ok := merchantMap[blogList[i].MerchantID]; ok {
//			resp.BlogList = append(resp.BlogList, Entity2DtoBlogDetailResp(blogList[i], merchant))
//		} else {
//			resp.BlogList = append(resp.BlogList, Entity2DtoBlogDetailResp(blogList[i], nil))
//		}
//	}
//	return resp
//}
