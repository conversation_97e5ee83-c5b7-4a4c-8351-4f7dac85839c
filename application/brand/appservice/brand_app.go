package appservice

import (
	"brandreviews/application/brand/dto"
	"brandreviews/domain/brand/service"
	"brandreviews/infra/ecode"
	"github.com/gin-gonic/gin"
)

type BrandAppService interface {
	GetBrandDetailById(ctx *gin.Context, id uint64) (*dto.BrandDetailResp, *ecode.Error)
	GetBrandDetailBySlug(ctx *gin.Context, slug string) (*dto.BrandDetailResp, *ecode.Error)
	GetBrandListByCondition(ctx *gin.Context, req *dto.GetBrandListReq) (*dto.BrandListResp, *ecode.Error)
	GetBrandCount(ctx *gin.Context) (int64, *ecode.Error)
}

type BrandAppServiceImpl struct {
	brandService service.BrandService
}

func NewBrandAppService(
	brandService service.BrandService,
) BrandAppService {
	return &BrandAppServiceImpl{
		brandService: brandService,
	}
}

func (app *BrandAppServiceImpl) GetBrandCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.brandService.GetBrandCount(ctx)
}

func (app *BrandAppServiceImpl) GetBrandDetailById(ctx *gin.Context, id uint64) (*dto.BrandDetailResp, *ecode.Error) {
	brand, err := app.brandService.GetBrandDetailById(ctx, id)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoBrandDetailResp(brand), nil
}

func (app *BrandAppServiceImpl) GetBrandDetailBySlug(ctx *gin.Context, slug string) (*dto.BrandDetailResp, *ecode.Error) {
	brand, err := app.brandService.GetBrandDetailBySlug(ctx, slug)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoBrandDetailResp(brand), nil
}

func (app *BrandAppServiceImpl) GetBrandListByCondition(ctx *gin.Context, req *dto.GetBrandListReq) (*dto.BrandListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetBrandList()
	brandList, total, err := app.brandService.GetBrandListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoBrandListResp(req.PageSize, req.Page, total, brandList), nil
}
