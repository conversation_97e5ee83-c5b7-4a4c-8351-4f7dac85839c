package appservice

import (
	"brandreviews/application/deal/dto"
	"brandreviews/domain/deal/service"
	"brandreviews/infra/ecode"
	"github.com/gin-gonic/gin"
)

type DealAppService interface {
	GetDealDetailById(ctx *gin.Context, id uint64) (*dto.DealDetailResp, *ecode.Error)
	GetDealDetailBySlug(ctx *gin.Context, slug string) (*dto.DealDetailResp, *ecode.Error)
	GetDealListByCondition(ctx *gin.Context, req *dto.GetDealListReq) (*dto.DealListResp, *ecode.Error)
	GetDealCount(ctx *gin.Context) (int64, *ecode.Error)
}

type DealAppServiceImpl struct {
	dealService service.DealService
}

func NewDealAppService(
	dealService service.DealService,
) DealAppService {
	return &DealAppServiceImpl{
		dealService: dealService,
	}
}

func (app *DealAppServiceImpl) GetDealCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.dealService.GetDealCount(ctx)
}

func (app *DealAppServiceImpl) GetDealDetailById(ctx *gin.Context, id uint64) (*dto.DealDetailResp, *ecode.Error) {
	deal, err := app.dealService.GetDealDetailById(ctx, id)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoDealDetailResp(deal), nil
}

func (app *DealAppServiceImpl) GetDealDetailBySlug(ctx *gin.Context, slug string) (*dto.DealDetailResp, *ecode.Error) {
	deal, err := app.dealService.GetDealDetailBySlug(ctx, slug)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoDealDetailResp(deal), nil
}

func (app *DealAppServiceImpl) GetDealListByCondition(ctx *gin.Context, req *dto.GetDealListReq) (*dto.DealListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetDealList()
	dealList, total, err := app.dealService.GetDealListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoDealListResp(req.PageSize, req.Page, total, dealList), nil
}
