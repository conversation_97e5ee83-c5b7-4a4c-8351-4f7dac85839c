package dto

import "brandreviews/domain/deal/entity"

type GetDealListReq struct {
	Page       int    `form:"page,default=1" binding:"min=1"`
	PageSize   int    `form:"page_size,default=30" binding:"min=1,max=100"`
	Featured   *bool  `form:"featured"`
	Active     *bool  `form:"active"`
	Verified   *bool  `form:"verified"`
	ValidOnly  *bool  `form:"valid_only"`
	BrandId    uint64 `form:"brand_id"`
	CategoryId uint64 `form:"category_id"`
	Search     string `form:"search"`
}

type DealDetailResp struct {
	Id            uint64  `json:"id"`
	Slug          string  `json:"slug"`
	Title         string  `json:"title"`
	Description   string  `json:"description"`
	DealUrl       string  `json:"deal_url"`
	ImageUrl      string  `json:"image_url"`
	OriginalPrice float64 `json:"original_price"`
	SalePrice     float64 `json:"sale_price"`
	DiscountType  string  `json:"discount_type"`
	DiscountValue float64 `json:"discount_value"`
	Featured      bool    `json:"featured"`
	Active        bool    `json:"active"`
	Verified      bool    `json:"verified"`
	StartDate     uint64  `json:"start_date"`
	EndDate       uint64  `json:"end_date"`
	BrandId       uint64  `json:"brand_id"`
	BrandSlug     string  `json:"brand_slug"`
	CategoryId    uint64  `json:"category_id"`
	CategorySlug  string  `json:"category_slug"`
}

type DealListResp struct {
	Total    int64             `json:"total"`
	Page     int               `json:"page"`
	PageSize int               `json:"page_size"`
	DealList []*DealDetailResp `json:"deal_list"`
}

func (req *GetDealListReq) Dto2ConditionGetDealList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Featured != nil {
		condition["featured"] = *req.Featured
	}
	if req.Active != nil {
		condition["active"] = *req.Active
	}
	if req.Verified != nil {
		condition["verified"] = *req.Verified
	}
	if req.ValidOnly != nil {
		condition["valid_only"] = *req.ValidOnly
	}
	if req.BrandId > 0 {
		condition["brand_id"] = req.BrandId
	}
	if req.CategoryId > 0 {
		condition["category_id"] = req.CategoryId
	}
	return condition
}

func Entity2DtoDealDetailResp(deal *entity.Deal) (resp *DealDetailResp) {
	resp = &DealDetailResp{}
	resp.Id = deal.Id
	resp.Slug = deal.Slug
	resp.Title = deal.Title
	resp.Description = deal.Description
	resp.DealUrl = deal.DealUrl
	resp.ImageUrl = deal.ImageUrl
	resp.OriginalPrice = deal.OriginalPrice
	resp.SalePrice = deal.SalePrice
	resp.DiscountType = deal.DiscountType
	resp.DiscountValue = deal.DiscountValue
	resp.Featured = deal.Featured
	resp.Active = deal.Active
	resp.Verified = deal.Verified
	resp.StartDate = deal.StartDate
	resp.EndDate = deal.EndDate
	resp.BrandId = deal.BrandId
	resp.BrandSlug = deal.BrandSlug
	resp.CategoryId = deal.CategoryId
	resp.CategorySlug = deal.CategorySlug
	return resp
}

func Entity2DtoDealListResp(pageSize int, page int, total int64, dealList []*entity.Deal) (resp *DealListResp) {
	resp = &DealListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range dealList {
		resp.DealList = append(resp.DealList, Entity2DtoDealDetailResp(dealList[i]))
	}
	return resp
}
