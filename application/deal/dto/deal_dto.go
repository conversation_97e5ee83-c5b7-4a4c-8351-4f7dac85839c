package dto

type GetDealListReq struct {
	Page       int    `form:"page,default=1" binding:"min=1"`
	PageSize   int    `form:"page_size,default=30" binding:"min=1,max=100"`
	Featured   *bool  `form:"featured"`
	Active     *bool  `form:"active"`
	Verified   *bool  `form:"verified"`
	ValidOnly  *bool  `form:"valid_only"`
	BrandId    uint64 `form:"brand_id"`
	CategoryId uint64 `form:"category_id"`
	Search     string `form:"search"`
}

type DealDetailResp struct {
	Id            uint64  `json:"id"`
	Slug          string  `json:"slug"`
	Title         string  `json:"title"`
	Description   string  `json:"description"`
	DealUrl       string  `json:"deal_url"`
	ImageUrl      string  `json:"image_url"`
	OriginalPrice float64 `json:"original_price"`
	SalePrice     float64 `json:"sale_price"`
	DiscountType  string  `json:"discount_type"`
	DiscountValue float64 `json:"discount_value"`
	Featured      bool    `json:"featured"`
	Active        bool    `json:"active"`
	Verified      bool    `json:"verified"`
	StartDate     uint64  `json:"start_date"`
	EndDate       uint64  `json:"end_date"`
	BrandId       uint64  `json:"brand_id"`
	BrandSlug     string  `json:"brand_slug"`
	CategoryId    uint64  `json:"category_id"`
	CategorySlug  string  `json:"category_slug"`
}

type DealListResp struct {
	Total    int64             `json:"total"`
	Page     int               `json:"page"`
	PageSize int               `json:"page_size"`
	DealList []*DealDetailResp `json:"deal_list"`
}
