package dto

import (
	"brandreviews/domain/tag/entity"
)

type GetTagListReq struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=30" binding:"min=1,max=100"`
	Featured *bool  `form:"featured"`
	Search   string `form:"search"`
}

type TagDetailResp struct {
	Id       uint64 `json:"id"`
	Slug     string `json:"slug"`
	Name     string `json:"name"`
	Featured bool   `json:"featured"`
}

type TagListResp struct {
	Total    int64            `json:"total"`
	Page     int              `json:"page"`
	PageSize int              `json:"page_size"`
	TagList  []*TagDetailResp `json:"tag_list"`
}

func (req *GetTagListReq) Dto2ConditionGetTagList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Featured != nil {
		condition["featured"] = *req.Featured
	}
	return condition
}

func Entity2DtoTagDetailResp(tag *entity.Tag) (resp *TagDetailResp) {
	resp = &TagDetailResp{}
	resp.Id = tag.Id
	resp.Slug = tag.Slug
	resp.Name = tag.Name
	resp.Featured = tag.Featured
	return resp
}

func Entity2DtoTagListResp(pageSize int, page int, total int64, tagList []*entity.Tag) (resp *TagListResp) {
	// 获取列表时可以接受没有商家信息
	resp = &TagListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range tagList {
		resp.TagList = append(resp.TagList, Entity2DtoTagDetailResp(tagList[i]))
	}
	return resp
}
