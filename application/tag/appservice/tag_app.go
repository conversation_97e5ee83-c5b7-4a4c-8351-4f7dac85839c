package appservice

import (
	"brandreviews/application/tag/dto"
	"brandreviews/domain/tag/service"
	"brandreviews/infra/ecode"
	"github.com/gin-gonic/gin"
)

type TagAppService interface {
	GetTagDetailById(ctx *gin.Context, id uint64) (*dto.TagDetailResp, *ecode.Error)
	GetTagListByCondition(ctx *gin.Context, req *dto.GetTagListReq) (*dto.TagListResp, *ecode.Error)
	GetTagCount(ctx *gin.Context) (int64, *ecode.Error)
}

type TagAppServiceImpl struct {
	tagService service.TagService
}

func NewTagAppService(
	tagService service.TagService,
) TagAppService {
	return &TagAppServiceImpl{
		tagService: tagService,
	}
}

func (app *TagAppServiceImpl) GetTagCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.tagService.GetTagCount(ctx)
}

func (app *TagAppServiceImpl) GetTagDetailById(ctx *gin.Context, id uint64) (*dto.TagDetailResp, *ecode.Error) {
	tag, err := app.tagService.GetTagDetailById(ctx, id)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoTagDetailResp(tag), nil
}

func (app *TagAppServiceImpl) GetTagListByCondition(ctx *gin.Context, req *dto.GetTagListReq) (*dto.TagListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetTagList()
	tagList, total, err := app.tagService.GetTagListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoTagListResp(req.PageSize, req.Page, total, tagList), nil
}
