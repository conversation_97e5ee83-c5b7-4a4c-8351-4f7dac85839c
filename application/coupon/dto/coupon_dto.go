package dto

import (
	"brandreviews/domain/coupon/entity"
)

type GetCouponListReq struct {
	Page          int    `form:"page,default=1" binding:"min=1"`
	PageSize      int    `form:"page_size,default=30" binding:"min=1,max=100"`
	Featured      *bool  `form:"featured"`
	Active        *bool  `form:"active"`
	Verified      *bool  `form:"verified"`
	ValidOnly     *bool  `form:"valid_only"`
	AvailableOnly *bool  `form:"available_only"`
	BrandId       uint64 `form:"brand_id"`
	CategoryId    uint64 `form:"category_id"`
	DiscountType  string `form:"discount_type"`
	Search        string `form:"search"`
}

type CouponDetailResp struct {
	Id             uint64  `json:"id"`
	Slug           string  `json:"slug"`
	Title          string  `json:"title"`
	Description    string  `json:"description"`
	Code           string  `json:"code"`
	CouponUrl      string  `json:"coupon_url"`
	DiscountType   string  `json:"discount_type"`
	DiscountValue  float64 `json:"discount_value"`
	MinOrderValue  float64 `json:"min_order_value"`
	MaxDiscount    float64 `json:"max_discount"`
	Featured       bool    `json:"featured"`
	Active         bool    `json:"active"`
	Verified       bool    `json:"verified"`
	StartDate      uint64  `json:"start_date"`
	EndDate        uint64  `json:"end_date"`
	UsageLimit     int     `json:"usage_limit"`
	UsedCount      int     `json:"used_count"`
	UserUsageLimit int     `json:"user_usage_limit"`
	BrandId        uint64  `json:"brand_id"`
	BrandSlug      string  `json:"brand_slug"`
	CategoryId     uint64  `json:"category_id"`
	CategorySlug   string  `json:"category_slug"`
}

type CouponListResp struct {
	Total      int64               `json:"total"`
	Page       int                 `json:"page"`
	PageSize   int                 `json:"page_size"`
	CouponList []*CouponDetailResp `json:"coupon_list"`
}

func (req *GetCouponListReq) Dto2ConditionGetCouponList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Featured != nil {
		condition["featured"] = *req.Featured
	}
	if req.Active != nil {
		condition["active"] = *req.Active
	}
	if req.Verified != nil {
		condition["verified"] = *req.Verified
	}
	if req.ValidOnly != nil {
		condition["valid_only"] = *req.ValidOnly
	}
	if req.AvailableOnly != nil {
		condition["available_only"] = *req.AvailableOnly
	}
	if req.BrandId > 0 {
		condition["brand_id"] = req.BrandId
	}
	if req.CategoryId > 0 {
		condition["category_id"] = req.CategoryId
	}
	if req.DiscountType != "" {
		condition["discount_type"] = req.DiscountType
	}
	return condition
}

func Entity2DtoCouponDetailResp(coupon *entity.Coupon) (resp *CouponDetailResp) {
	resp = &CouponDetailResp{}
	resp.Id = coupon.Id
	resp.Slug = coupon.Slug
	resp.Title = coupon.Title
	resp.Description = coupon.Description
	resp.Code = coupon.Code
	resp.CouponUrl = coupon.CouponUrl
	resp.DiscountType = coupon.DiscountType
	resp.DiscountValue = coupon.DiscountValue
	resp.MinOrderValue = coupon.MinOrderValue
	resp.MaxDiscount = coupon.MaxDiscount
	resp.Featured = coupon.Featured
	resp.Active = coupon.Active
	resp.Verified = coupon.Verified
	resp.StartDate = coupon.StartDate
	resp.EndDate = coupon.EndDate
	resp.UsageLimit = coupon.UsageLimit
	resp.UsedCount = coupon.UsedCount
	resp.UserUsageLimit = coupon.UserUsageLimit
	resp.BrandId = coupon.BrandId
	resp.BrandSlug = coupon.BrandSlug
	resp.CategoryId = coupon.CategoryId
	resp.CategorySlug = coupon.CategorySlug
	return resp
}

func Entity2DtoCouponListResp(pageSize int, page int, total int64, couponList []*entity.Coupon) (resp *CouponListResp) {
	resp = &CouponListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range couponList {
		resp.CouponList = append(resp.CouponList, Entity2DtoCouponDetailResp(couponList[i]))
	}
	return resp
}
