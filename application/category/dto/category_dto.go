package dto

import "brandreviews/domain/category/entity"

type GetCategoryListReq struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=30" binding:"min=1,max=100"`
	Featured *bool  `form:"featured"`
	Search   string `form:"search"`
}

type CategoryDetailResp struct {
	Id          uint64 `json:"id"`
	Slug        string `json:"slug"`
	Name        string `json:"name"`
	Icon        string `json:"icon"`
	Description string `json:"description"`
	Featured    bool   `json:"featured"`
}

type CategoryListResp struct {
	Total        int64                 `json:"total"`
	Page         int                   `json:"page"`
	PageSize     int                   `json:"page_size"`
	CategoryList []*CategoryDetailResp `json:"category_list"`
}

func (req *GetCategoryListReq) Dto2ConditionGetCategoryList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Featured != nil {
		condition["featured"] = *req.Featured
	}
	return condition
}

func Entity2DtoCategoryDetailResp(category *entity.Category) (resp *CategoryDetailResp) {
	resp = &CategoryDetailResp{}
	resp.Id = category.Id
	resp.Slug = category.Slug
	resp.Name = category.Name
	resp.Featured = category.Featured
	resp.Icon = category.Icon
	resp.Description = category.Description
	return resp
}

func Entity2DtoCategoryListResp(pageSize int, page int, total int64, categoryList []*entity.Category) (resp *CategoryListResp) {
	// 获取列表时可以接受没有商家信息
	resp = &CategoryListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range categoryList {
		resp.CategoryList = append(resp.CategoryList, Entity2DtoCategoryDetailResp(categoryList[i]))
	}
	return resp
}
