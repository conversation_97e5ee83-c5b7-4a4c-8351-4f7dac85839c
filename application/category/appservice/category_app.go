package appservice

import (
	"brandreviews/application/category/dto"
	"brandreviews/domain/category/service"
	"brandreviews/infra/ecode"
	"github.com/gin-gonic/gin"
)

type CategoryAppService interface {
	GetCategoryDetailById(ctx *gin.Context, id uint64) (*dto.CategoryDetailResp, *ecode.Error)
	GetCategoryListByCondition(ctx *gin.Context, req *dto.GetCategoryListReq) (*dto.CategoryListResp, *ecode.Error)
	GetCategoryCount(ctx *gin.Context) (int64, *ecode.Error)
}

type CategoryAppServiceImpl struct {
	CategoryService service.CategoryService
}

func NewCategoryAppService(
	CategoryService service.CategoryService,
) CategoryAppService {
	return &CategoryAppServiceImpl{
		CategoryService: CategoryService,
	}
}

func (app *CategoryAppServiceImpl) GetCategoryCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.CategoryService.GetCategoryCount(ctx)
}

func (app *CategoryAppServiceImpl) GetCategoryDetailById(ctx *gin.Context, id uint64) (*dto.CategoryDetailResp, *ecode.Error) {
	category, err := app.CategoryService.GetCategoryDetailById(ctx, id)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoCategoryDetailResp(category), nil
}

func (app *CategoryAppServiceImpl) GetCategoryListByCondition(ctx *gin.Context, req *dto.GetCategoryListReq) (*dto.CategoryListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetCategoryList()
	categoryList, total, err := app.CategoryService.GetCategoryListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoCategoryListResp(req.PageSize, req.Page, total, categoryList), nil
}
